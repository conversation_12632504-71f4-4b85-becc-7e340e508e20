import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score

# Distância Haversine (km)
def haversine(lat1, lon1, lat2, lon2):
    R = 6371.0
    p1 = np.radians(lat1); p2 = np.radians(lat2)
    dlat = np.radians(lat2 - lat1)
    dlon = np.radians(lon2 - lon1)
    a = np.sin(dlat/2)**2 + np.cos(p1)*np.cos(p2)*np.sin(dlon/2)**2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))
    return R * c

def nearest_city(centroid, df, exclude_existing=False):
    lat, lon = centroid
    dists = haversine(lat, lon, df['lat'].values, df['lon'].values)
    order = np.argsort(dists)
    if not exclude_existing:
        idx = int(order[0])
        row = df.iloc[idx]
        return row['city'], row['state'], float(dists[order[0]])
    # procurar a mais próxima que NÃO seja loja existente
    for idx in order:
        row = df.iloc[int(idx)]
        if not row.get('existing_store', False):
            return row['city'], row['state'], float(dists[int(idx)])
    # fallback: se todas forem existentes, retorna a mais próxima mesmo assim
    idx = int(order[0])
    row = df.iloc[idx]
    return row['city'], row['state'], float(dists[idx])


sp_data = [
    {'city': 'São Paulo', 'state': 'SP', 'lat': -23.5505, 'lon': -46.6333, 'population': 12000000, 'existing_store': True},
    {'city': 'Guarulhos', 'state': 'SP', 'lat': -23.4543, 'lon': -46.5337, 'population': 1400000, 'existing_store': False},
    {'city': 'Osasco', 'state': 'SP', 'lat': -23.532, 'lon': -46.7913, 'population': 700000, 'existing_store': False},
    {'city': 'Santo André', 'state': 'SP', 'lat': -23.6637, 'lon': -46.5383, 'population': 720000, 'existing_store': False},
    {'city': 'São Bernardo do Campo', 'state': 'SP', 'lat': -23.689, 'lon': -46.5646, 'population': 840000, 'existing_store': False},
    {'city': 'Diadema', 'state': 'SP', 'lat': -23.6813, 'lon': -46.62, 'population': 420000, 'existing_store': False},
    {'city': 'Barueri', 'state': 'SP', 'lat': -23.5057, 'lon': -46.879, 'population': 280000, 'existing_store': False},
    {'city': 'Jundiaí', 'state': 'SP', 'lat': -23.1857, 'lon': -46.8978, 'population': 440000, 'existing_store': True},
    {'city': 'Campinas', 'state': 'SP', 'lat': -22.9056, 'lon': -47.0608, 'population': 1300000, 'existing_store': True},
    {'city': 'Americana', 'state': 'SP', 'lat': -22.7377, 'lon': -47.3331, 'population': 240000, 'existing_store': False},
    {'city': 'Limeira', 'state': 'SP', 'lat': -22.565, 'lon': -47.4009, 'population': 310000, 'existing_store': False},
    {'city': 'Piracicaba', 'state': 'SP', 'lat': -22.7338, 'lon': -47.6476, 'population': 410000, 'existing_store': False},
    {'city': 'Rio Claro', 'state': 'SP', 'lat': -22.4111, 'lon': -47.561, 'population': 210000, 'existing_store': False},
    {'city': 'Sorocaba', 'state': 'SP', 'lat': -23.5015, 'lon': -47.4526, 'population': 700000, 'existing_store': True},
    {'city': 'Indaiatuba', 'state': 'SP', 'lat': -23.0907, 'lon': -47.2181, 'population': 270000, 'existing_store': False},
    {'city': 'Itu', 'state': 'SP', 'lat': -23.2665, 'lon': -47.2992, 'population': 180000, 'existing_store': False},
    {'city': 'Itapetininga', 'state': 'SP', 'lat': -23.5917, 'lon': -48.053, 'population': 170000, 'existing_store': False},
    {'city': 'Tatuí', 'state': 'SP', 'lat': -23.3556, 'lon': -47.8569, 'population': 120000, 'existing_store': False},
    {'city': 'Botucatu', 'state': 'SP', 'lat': -22.8859, 'lon': -48.4446, 'population': 150000, 'existing_store': False},
    {'city': 'Bauru', 'state': 'SP', 'lat': -22.3246, 'lon': -49.0871, 'population': 380000, 'existing_store': True},
    {'city': 'Jaú', 'state': 'SP', 'lat': -22.2966, 'lon': -48.5583, 'population': 150000, 'existing_store': False},
    {'city': 'Marília', 'state': 'SP', 'lat': -22.2171, 'lon': -49.9501, 'population': 230000, 'existing_store': False},
    {'city': 'Ourinhos', 'state': 'SP', 'lat': -22.9797, 'lon': -49.8719, 'population': 115000, 'existing_store': False},
    {'city': 'Assis', 'state': 'SP', 'lat': -22.6619, 'lon': -50.4121, 'population': 105000, 'existing_store': False},
    {'city': 'Presidente Prudente', 'state': 'SP', 'lat': -22.1207, 'lon': -51.3925, 'population': 230000, 'existing_store': False},
    {'city': 'Araçatuba', 'state': 'SP', 'lat': -21.206, 'lon': -50.432, 'population': 200000, 'existing_store': False},
    {'city': 'Birigui', 'state': 'SP', 'lat': -21.2887, 'lon': -50.3432, 'population': 120000, 'existing_store': False},
    {'city': 'Penápolis', 'state': 'SP', 'lat': -21.419, 'lon': -50.0773, 'population': 65000, 'existing_store': False},
    {'city': 'Andradina', 'state': 'SP', 'lat': -20.8967, 'lon': -51.3795, 'population': 57000, 'existing_store': False},
    {'city': 'São José do Rio Preto', 'state': 'SP', 'lat': -20.8113, 'lon': -49.3758, 'population': 470000, 'existing_store': True},
    {'city': 'Catanduva', 'state': 'SP', 'lat': -21.1316, 'lon': -48.977, 'population': 120000, 'existing_store': False},
    {'city': 'Barretos', 'state': 'SP', 'lat': -20.5531, 'lon': -48.567, 'population': 120000, 'existing_store': False},
    {'city': 'Franca', 'state': 'SP', 'lat': -20.5386, 'lon': -47.4004, 'population': 370000, 'existing_store': False},
    {'city': 'Ribeirão Preto', 'state': 'SP', 'lat': -21.1775, 'lon': -47.8103, 'population': 720000, 'existing_store': True},
    {'city': 'São Carlos', 'state': 'SP', 'lat': -22.0087, 'lon': -47.8976, 'population': 250000, 'existing_store': False},
    {'city': 'Araraquara', 'state': 'SP', 'lat': -21.7845, 'lon': -48.178, 'population': 240000, 'existing_store': False},
    {'city': 'Mogi das Cruzes', 'state': 'SP', 'lat': -23.5208, 'lon': -46.1855, 'population': 460000, 'existing_store': False},
    {'city': 'Suzano', 'state': 'SP', 'lat': -23.5439, 'lon': -46.311, 'population': 300000, 'existing_store': False},
    {'city': 'Bragança Paulista', 'state': 'SP', 'lat': -22.9527, 'lon': -46.5419, 'population': 180000, 'existing_store': False},
    {'city': 'Atibaia', 'state': 'SP', 'lat': -23.1171, 'lon': -46.5548, 'population': 150000, 'existing_store': False},
    {'city': 'Jacareí', 'state': 'SP', 'lat': -23.2983, 'lon': -45.9658, 'population': 240000, 'existing_store': False},
    {'city': 'São José dos Campos', 'state': 'SP', 'lat': -23.1896, 'lon': -45.8841, 'population': 750000, 'existing_store': True},
    {'city': 'Taubaté', 'state': 'SP', 'lat': -23.0205, 'lon': -45.5569, 'population': 320000, 'existing_store': False},
    {'city': 'Pindamonhangaba', 'state': 'SP', 'lat': -22.9235, 'lon': -45.4614, 'population': 170000, 'existing_store': False},
    {'city': 'Santos', 'state': 'SP', 'lat': -23.959, 'lon': -46.3336, 'population': 430000, 'existing_store': True},
    {'city': 'São Vicente', 'state': 'SP', 'lat': -23.9631, 'lon': -46.3919, 'population': 370000, 'existing_store': False},
    {'city': 'Praia Grande', 'state': 'SP', 'lat': -24.0058, 'lon': -46.402, 'population': 330000, 'existing_store': False}
]
df = pd.DataFrame(sp_data)
print("Total de cidades:", len(df))
df.head()


plt.figure(figsize=(9,6))
mask = df['existing_store']
plt.scatter(df.loc[~mask, 'lon'], df.loc[~mask, 'lat'], s=25, alpha=0.8, label='Cidades sem loja')
plt.scatter(df.loc[mask, 'lon'], df.loc[mask, 'lat'], s=60, alpha=0.9, marker='s', label='Lojas existentes')
for _, row in df.iterrows():
    if row['existing_store']:
        plt.annotate(row['city'], (row['lon'], row['lat']), fontsize=7, alpha=0.8)
plt.title("Cidades de SP · Lojas existentes destacadas")
plt.xlabel("Longitude"); plt.ylabel("Latitude")
plt.grid(True, linestyle='--', alpha=0.3); plt.legend()
plt.show()


df_existing = df[df['existing_store']].copy()
print("Lojas existentes:", len(df_existing))
df_existing[['city','state','lat','lon']].reset_index(drop=True)

def run_cd_scenario(k, df_existing):
    X = df_existing[['lat','lon']].values
    km = KMeans(n_clusters=k, n_init='auto', random_state=42)
    labels = km.fit_predict(X)
    centroids = km.cluster_centers_
    inertia = km.inertia_
    sil = silhouette_score(X, labels) if k > 1 and k < len(X) else float('nan')

    # Plot clusters (apenas lojas existentes)
    plt.figure(figsize=(9,6))
    for i in range(k):
        pts = X[labels == i]
        plt.scatter(pts[:,1], pts[:,0], s=45, alpha=0.9, label=f"Cluster {i+1}")
    plt.scatter(centroids[:,1], centroids[:,0], s=200, marker='X', edgecolor='black', linewidth=1, label="Centróides (CDs)")
    plt.title(f"CDs para lojas existentes — k={k}")
    plt.xlabel("Longitude"); plt.ylabel("Latitude")
    plt.grid(True, linestyle='--', alpha=0.3); plt.legend()
    plt.show()

    # Cidades candidatas aos CDs (mais próximas dos centróides)
    candidatos = []
    for c in centroids:
        cidade, estado, dist_km = nearest_city(c, df_existing, exclude_existing=False)
        candidatos.append((cidade, estado, dist_km, c[0], c[1]))
    out = pd.DataFrame(candidatos, columns=["cidade_candidata","estado","dist_km","centroid_lat","centroid_lon"])
    display(out.sort_values("dist_km"))
    print(f"Inércia (WCSS): {inertia:.2f} | Silhouette: {sil:.3f}")
    return centroids, labels, inertia, sil


cA1 = run_cd_scenario(1, df_existing)

cA2 = run_cd_scenario(2, df_existing)

cA3 = run_cd_scenario(3, df_existing)

def run_weighted_expansion(k, df_full):
    X = df_full[['lat','lon']].values
    w = df_full['population'].values.astype(float)
    km = KMeans(n_clusters=k, n_init='auto', random_state=42)
    labels = km.fit_predict(X, sample_weight=w)
    centroids = km.cluster_centers_
    inertia = km.inertia_

    # silhouette sem peso (aprox.) — apenas para referência
    sil = silhouette_score(X, labels) if k > 1 and k < len(X) else float('nan')

    # Plot: cidades sem loja (oportunidade) vs existentes
    plt.figure(figsize=(9,6))
    mask = df_full['existing_store']
    plt.scatter(df_full.loc[~mask,'lon'], df_full.loc[~mask,'lat'], s=20, alpha=0.8, label="Cidades sem loja")
    plt.scatter(df_full.loc[mask,'lon'], df_full.loc[mask,'lat'], s=60, alpha=0.9, marker='s', label="Lojas existentes")
    plt.scatter(centroids[:,1], centroids[:,0], s=220, marker='X', edgecolor='black', linewidth=1, label="Centróides (demanda)")
    plt.title(f"Clusters de demanda (população) — k={k}")
    plt.xlabel("Longitude"); plt.ylabel("Latitude")
    plt.grid(True, linestyle='--', alpha=0.3); plt.legend()
    plt.show()

    # Sugerir cidades candidatas (preferindo não existentes)
    candidatos = []
    for c in centroids:
        cidade, estado, dist_km = nearest_city(c, df_full, exclude_existing=True)
        candidatos.append((cidade, estado, dist_km, c[0], c[1]))
    out = pd.DataFrame(candidatos, columns=["cidade_candidata_nova_loja","estado","dist_km","centroid_lat","centroid_lon"])
    display(out.sort_values("dist_km"))
    print(f"Inércia (WCSS): {inertia:.2f} | Silhouette (aprox. sem peso): {sil:.3f}")
    return centroids, labels, inertia, sil

cB1 = run_weighted_expansion(1, df)

cB2 = run_weighted_expansion(2, df)

cB3 = run_weighted_expansion(3, df)

# Análise de Potencial de Vendas
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Filtrar apenas cidades sem lojas existentes
df_candidates = df[~df['existing_store']].copy()

print(f"Total de cidades candidatas (sem loja existente): {len(df_candidates)}")
print(f"Total de lojas existentes: {df['existing_store'].sum()}")

# Calcular distância mínima para lojas existentes
existing_stores = df[df['existing_store']]

def min_distance_to_existing_store(row, existing_stores):
    """Calcula a distância mínima de uma cidade para qualquer loja existente"""
    distances = []
    for _, store in existing_stores.iterrows():
        dist = haversine(row['lat'], row['lon'], store['lat'], store['lon'])
        distances.append(dist)
    return min(distances) if distances else float('inf')

# Adicionar coluna de distância mínima para lojas existentes
df_candidates['min_dist_existing'] = df_candidates.apply(
    lambda row: min_distance_to_existing_store(row, existing_stores), axis=1
)

df_candidates.head()

# Algoritmo de Pontuação para Potencial de Vendas

def calculate_sales_potential_score(df_candidates):
    """Calcula score de potencial de vendas baseado em múltiplos fatores"""
    df_score = df_candidates.copy()
    
    # 1. Score de População (40% do peso total)
    # Normalizar população entre 0-1 e aplicar peso
    pop_min, pop_max = df_score['population'].min(), df_score['population'].max()
    df_score['population_score'] = (df_score['population'] - pop_min) / (pop_max - pop_min)
    
    # 2. Score de Distância (30% do peso total)
    # Maior distância de lojas existentes = melhor (evita canibalização)
    dist_min, dist_max = df_score['min_dist_existing'].min(), df_score['min_dist_existing'].max()
    df_score['distance_score'] = (df_score['min_dist_existing'] - dist_min) / (dist_max - dist_min)
    
    # 3. Score de Densidade Regional (30% do peso total)
    # Calcular densidade populacional em um raio de 50km
    def regional_density(row, df_all):
        total_pop = 0
        for _, other in df_all.iterrows():
            dist = haversine(row['lat'], row['lon'], other['lat'], other['lon'])
            if dist <= 50:  # raio de 50km
                total_pop += other['population']
        return total_pop
    
    df_score['regional_population'] = df_score.apply(
        lambda row: regional_density(row, df), axis=1
    )
    
    reg_min, reg_max = df_score['regional_population'].min(), df_score['regional_population'].max()
    df_score['regional_density_score'] = (df_score['regional_population'] - reg_min) / (reg_max - reg_min)
    
    # Score Final Ponderado
    df_score['sales_potential_score'] = (
        0.40 * df_score['population_score'] +
        0.30 * df_score['distance_score'] +
        0.30 * df_score['regional_density_score']
    )
    
    return df_score

# Calcular scores
df_scored = calculate_sales_potential_score(df_candidates)

# Mostrar estatísticas dos scores
print("Estatísticas dos Scores:")
print(df_scored[['population_score', 'distance_score', 'regional_density_score', 'sales_potential_score']].describe())

# TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS

top5_cities = df_scored.nlargest(5, 'sales_potential_score')

print("🏆 TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS NO BRASIL")
print("=" * 60)

for i, (_, city) in enumerate(top5_cities.iterrows(), 1):
    print(f"{i}º LUGAR: {city['city']} - {city['state']}")
    print(f"   📊 Score de Potencial: {city['sales_potential_score']:.3f}")
    print(f"   👥 População: {city['population']:,} habitantes")
    print(f"   📍 Distância da loja mais próxima: {city['min_dist_existing']:.1f} km")
    print(f"   🏘️  População regional (50km): {city['regional_population']:,} habitantes")
    print()

# Criar DataFrame resumo para visualização
summary_df = top5_cities[['city', 'state', 'population', 'min_dist_existing', 
                         'regional_population', 'sales_potential_score']].copy()
summary_df.columns = ['Cidade', 'Estado', 'População', 'Dist_Loja_Próxima_km', 
                     'Pop_Regional_50km', 'Score_Potencial']
summary_df = summary_df.round(3)

print("\n📋 RESUMO EXECUTIVO - TOP 5:")
display(summary_df)

# Visualizações da Análise

# 1. Gráfico de barras - Top 5 cidades por score
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
cities_names = [f"{city['city']}\n{city['state']}" for _, city in top5_cities.iterrows()]
scores = top5_cities['sales_potential_score'].values
colors = plt.cm.viridis(np.linspace(0, 1, 5))

bars = plt.bar(range(5), scores, color=colors)
plt.title('Top 5 Cidades - Score de Potencial de Vendas', fontsize=14, fontweight='bold')
plt.xlabel('Cidades')
plt.ylabel('Score de Potencial')
plt.xticks(range(5), cities_names, rotation=45, ha='right')
plt.grid(True, alpha=0.3)

# Adicionar valores nas barras
for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{height:.3f}', ha='center', va='bottom', fontweight='bold')

# 2. Scatter plot - População vs Distância
plt.subplot(1, 2, 2)
plt.scatter(df_candidates['population'], df_candidates['min_dist_existing'], 
           c=df_candidates['sales_potential_score'], cmap='viridis', 
           s=60, alpha=0.7)
plt.colorbar(label='Score de Potencial')

# Destacar top 5
for _, city in top5_cities.iterrows():
    plt.scatter(city['population'], city['min_dist_existing'], 
               s=200, facecolors='none', edgecolors='red', linewidth=2)
    plt.annotate(city['city'], (city['population'], city['min_dist_existing']),
                xytext=(5, 5), textcoords='offset points', fontsize=8, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

plt.title('População vs Distância de Lojas Existentes', fontsize=14, fontweight='bold')
plt.xlabel('População')
plt.ylabel('Distância da Loja Mais Próxima (km)')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Mapa Geográfico com Top 5 Destacado

plt.figure(figsize=(12, 8))

# Plotar todas as cidades
mask_existing = df['existing_store']
mask_candidates = ~df['existing_store']

# Cidades sem loja (candidatas)
plt.scatter(df.loc[mask_candidates, 'lon'], df.loc[mask_candidates, 'lat'], 
           s=df.loc[mask_candidates, 'population']/10000, alpha=0.6, 
           c='lightblue', label='Cidades sem loja', edgecolors='gray', linewidth=0.5)

# Lojas existentes
plt.scatter(df.loc[mask_existing, 'lon'], df.loc[mask_existing, 'lat'], 
           s=df.loc[mask_existing, 'population']/8000, alpha=0.8, 
           c='red', marker='s', label='Lojas existentes', edgecolors='darkred', linewidth=1)

# Destacar TOP 5 com maior potencial
plt.scatter(top5_cities['lon'], top5_cities['lat'], 
           s=top5_cities['population']/5000, alpha=0.9, 
           c='gold', marker='*', label='TOP 5 Potencial', 
           edgecolors='orange', linewidth=2)

# Anotar TOP 5
for i, (_, city) in enumerate(top5_cities.iterrows(), 1):
    plt.annotate(f"{i}º {city['city']}", 
                (city['lon'], city['lat']),
                xytext=(10, 10), textcoords='offset points',
                fontsize=10, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='gold', alpha=0.8),
                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

# Anotar lojas existentes
for _, store in existing_stores.iterrows():
    plt.annotate(store['city'], (store['lon'], store['lat']),
                xytext=(5, 5), textcoords='offset points', fontsize=8, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='pink', alpha=0.7))

plt.title('Mapa de Potencial de Vendas - Estado de São Paulo\n(Tamanho dos pontos = População)', 
         fontsize=16, fontweight='bold')
plt.xlabel('Longitude', fontsize=12)
plt.ylabel('Latitude', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.3)
plt.legend(loc='upper right', fontsize=10)
plt.tight_layout()
plt.show()

# Métricas Detalhadas das Top 5 Cidades

print("📊 ANÁLISE DETALHADA DAS TOP 5 CIDADES")
print("=" * 70)

total_population_top5 = top5_cities['population'].sum()
total_regional_pop = top5_cities['regional_population'].sum()
avg_distance = top5_cities['min_dist_existing'].mean()

print(f"📈 População Total das Top 5: {total_population_top5:,} habitantes")
print(f"🌍 População Regional Total (50km): {total_regional_pop:,} habitantes")
print(f"📏 Distância Média de Lojas Existentes: {avg_distance:.1f} km")
print(f"💰 Potencial de Mercado Estimado: {total_population_top5 * 0.15:,.0f} clientes potenciais")
print(f"   (assumindo 15% de penetração de mercado)")

print("\n🎯 RECOMENDAÇÕES ESTRATÉGICAS:")
print("-" * 40)

for i, (_, city) in enumerate(top5_cities.iterrows(), 1):
    market_potential = city['population'] * 0.15
    if city['min_dist_existing'] > 50:
        competition = "Baixa"
    elif city['min_dist_existing'] > 30:
        competition = "Média"
    else:
        competition = "Alta"
    
    print(f"{i}º {city['city']} - {city['state']}:")
    print(f"   • Mercado Potencial: {market_potential:,.0f} clientes")
    print(f"   • Competição Local: {competition}")
    print(f"   • Prioridade: {'🔥 ALTA' if i <= 2 else '⭐ MÉDIA' if i <= 4 else '✅ BAIXA'}")
    print()

# Tabela Comparativa Completa

# Criar tabela com todas as métricas importantes
comparison_df = top5_cities[[
    'city', 'state', 'population', 'min_dist_existing', 
    'regional_population', 'population_score', 'distance_score', 
    'regional_density_score', 'sales_potential_score'
]].copy()

comparison_df.columns = [
    'Cidade', 'Estado', 'População', 'Dist_Loja_Próxima_km', 
    'Pop_Regional_50km', 'Score_População', 'Score_Distância', 
    'Score_Densidade', 'Score_Final'
]

# Adicionar ranking
comparison_df.insert(0, 'Ranking', range(1, 6))

# Formatar números
comparison_df['População'] = comparison_df['População'].apply(lambda x: f"{x:,}")
comparison_df['Pop_Regional_50km'] = comparison_df['Pop_Regional_50km'].apply(lambda x: f"{x:,}")
comparison_df['Dist_Loja_Próxima_km'] = comparison_df['Dist_Loja_Próxima_km'].round(1)

# Arredondar scores
score_cols = ['Score_População', 'Score_Distância', 'Score_Densidade', 'Score_Final']
for col in score_cols:
    comparison_df[col] = comparison_df[col].round(3)

print("📋 TABELA COMPARATIVA COMPLETA - TOP 5 CIDADES")
print("=" * 80)
display(comparison_df)

# Salvar resultado em CSV
comparison_df.to_csv('top5_cidades_potencial_vendas.csv', index=False, encoding='utf-8')
print("\n💾 Resultado salvo em: top5_cidades_potencial_vendas.csv")

# Explicação da Metodologia

print("🔬 METODOLOGIA DE ANÁLISE")
print("=" * 50)
print()
print("A análise de potencial de vendas foi baseada em 3 fatores principais:")
print()
print("1️⃣ SCORE DE POPULAÇÃO (40% do peso):")
print("   • Maior população = maior mercado consumidor potencial")
print("   • Normalizado entre 0-1 baseado na população mínima e máxima")
print()
print("2️⃣ SCORE DE DISTÂNCIA (30% do peso):")
print("   • Maior distância de lojas existentes = menor canibalização")
print("   • Evita competição direta com lojas próprias")
print("   • Garante cobertura geográfica adequada")
print()
print("3️⃣ SCORE DE DENSIDADE REGIONAL (30% do peso):")
print("   • População total em um raio de 50km")
print("   • Considera o mercado regional ampliado")
print("   • Importante para logística e distribuição")
print()
print("🧮 FÓRMULA FINAL:")
print("Score = 0.40×Score_Pop + 0.30×Score_Dist + 0.30×Score_Densidade")
print()
print("✅ CRITÉRIOS DE EXCLUSÃO:")
print("   • Apenas cidades SEM lojas existentes foram consideradas")
print("   • Evita canibalização do negócio atual")
print()
print("📊 DADOS UTILIZADOS:")
print(f"   • Total de cidades analisadas: {len(df)}")
print(f"   • Cidades com lojas existentes: {df['existing_store'].sum()}")
print(f"   • Cidades candidatas: {len(df_candidates)}")
print(f"   • Estado analisado: São Paulo")

# Análise de Potencial de Vendas
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Filtrar apenas cidades sem lojas existentes
df_candidates = df[~df['existing_store']].copy()

print(f"Total de cidades candidatas (sem loja existente): {len(df_candidates)}")
print(f"Total de lojas existentes: {df['existing_store'].sum()}")

# Calcular distância mínima para lojas existentes
existing_stores = df[df['existing_store']]

def min_distance_to_existing_store(row, existing_stores):
    """Calcula a distância mínima de uma cidade para qualquer loja existente"""
    distances = []
    for _, store in existing_stores.iterrows():
        dist = haversine(row['lat'], row['lon'], store['lat'], store['lon'])
        distances.append(dist)
    return min(distances) if distances else float('inf')

# Adicionar coluna de distância mínima para lojas existentes
df_candidates['min_dist_existing'] = df_candidates.apply(
    lambda row: min_distance_to_existing_store(row, existing_stores), axis=1
)

df_candidates.head()

# Algoritmo de Pontuação para Potencial de Vendas

def calculate_sales_potential_score(df_candidates):
    """Calcula score de potencial de vendas baseado em múltiplos fatores"""
    df_score = df_candidates.copy()
    
    # 1. Score de População (40% do peso total)
    # Normalizar população entre 0-1 e aplicar peso
    pop_min, pop_max = df_score['population'].min(), df_score['population'].max()
    df_score['population_score'] = (df_score['population'] - pop_min) / (pop_max - pop_min)
    
    # 2. Score de Distância (30% do peso total)
    # Maior distância de lojas existentes = melhor (evita canibalização)
    dist_min, dist_max = df_score['min_dist_existing'].min(), df_score['min_dist_existing'].max()
    df_score['distance_score'] = (df_score['min_dist_existing'] - dist_min) / (dist_max - dist_min)
    
    # 3. Score de Densidade Regional (30% do peso total)
    # Calcular densidade populacional em um raio de 50km
    def regional_density(row, df_all):
        total_pop = 0
        for _, other in df_all.iterrows():
            dist = haversine(row['lat'], row['lon'], other['lat'], other['lon'])
            if dist <= 50:  # raio de 50km
                total_pop += other['population']
        return total_pop
    
    df_score['regional_population'] = df_score.apply(
        lambda row: regional_density(row, df), axis=1
    )
    
    reg_min, reg_max = df_score['regional_population'].min(), df_score['regional_population'].max()
    df_score['regional_density_score'] = (df_score['regional_population'] - reg_min) / (reg_max - reg_min)
    
    # Score Final Ponderado
    df_score['sales_potential_score'] = (
        0.40 * df_score['population_score'] +
        0.30 * df_score['distance_score'] +
        0.30 * df_score['regional_density_score']
    )
    
    return df_score

# Calcular scores
df_scored = calculate_sales_potential_score(df_candidates)

# Mostrar estatísticas dos scores
print("Estatísticas dos Scores:")
print(df_scored[['population_score', 'distance_score', 'regional_density_score', 'sales_potential_score']].describe())

# TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS

top5_cities = df_scored.nlargest(5, 'sales_potential_score')

print("🏆 TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS NO BRASIL")
print("=" * 60)

for i, (_, city) in enumerate(top5_cities.iterrows(), 1):
    print(f"{i}º LUGAR: {city['city']} - {city['state']}")
    print(f"   📊 Score de Potencial: {city['sales_potential_score']:.3f}")
    print(f"   👥 População: {city['population']:,} habitantes")
    print(f"   📍 Distância da loja mais próxima: {city['min_dist_existing']:.1f} km")
    print(f"   🏘️  População regional (50km): {city['regional_population']:,} habitantes")
    print()

# Criar DataFrame resumo para visualização
summary_df = top5_cities[['city', 'state', 'population', 'min_dist_existing', 
                         'regional_population', 'sales_potential_score']].copy()
summary_df.columns = ['Cidade', 'Estado', 'População', 'Dist_Loja_Próxima_km', 
                     'Pop_Regional_50km', 'Score_Potencial']
summary_df = summary_df.round(3)

print("\n📋 RESUMO EXECUTIVO - TOP 5:")
display(summary_df)

# Visualizações da Análise

# 1. Gráfico de barras - Top 5 cidades por score
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
cities_names = [f"{city['city']}\n{city['state']}" for _, city in top5_cities.iterrows()]
scores = top5_cities['sales_potential_score'].values
colors = plt.cm.viridis(np.linspace(0, 1, 5))

bars = plt.bar(range(5), scores, color=colors)
plt.title('Top 5 Cidades - Score de Potencial de Vendas', fontsize=14, fontweight='bold')
plt.xlabel('Cidades')
plt.ylabel('Score de Potencial')
plt.xticks(range(5), cities_names, rotation=45, ha='right')
plt.grid(True, alpha=0.3)

# Adicionar valores nas barras
for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{height:.3f}', ha='center', va='bottom', fontweight='bold')

# 2. Scatter plot - População vs Distância
plt.subplot(1, 2, 2)
plt.scatter(df_candidates['population'], df_candidates['min_dist_existing'], 
           c=df_candidates['sales_potential_score'], cmap='viridis', 
           s=60, alpha=0.7)
plt.colorbar(label='Score de Potencial')

# Destacar top 5
for _, city in top5_cities.iterrows():
    plt.scatter(city['population'], city['min_dist_existing'], 
               s=200, facecolors='none', edgecolors='red', linewidth=2)
    plt.annotate(city['city'], (city['population'], city['min_dist_existing']),
                xytext=(5, 5), textcoords='offset points', fontsize=8, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

plt.title('População vs Distância de Lojas Existentes', fontsize=14, fontweight='bold')
plt.xlabel('População')
plt.ylabel('Distância da Loja Mais Próxima (km)')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Mapa Geográfico com Top 5 Destacado

plt.figure(figsize=(12, 8))

# Plotar todas as cidades
mask_existing = df['existing_store']
mask_candidates = ~df['existing_store']

# Cidades sem loja (candidatas)
plt.scatter(df.loc[mask_candidates, 'lon'], df.loc[mask_candidates, 'lat'], 
           s=df.loc[mask_candidates, 'population']/10000, alpha=0.6, 
           c='lightblue', label='Cidades sem loja', edgecolors='gray', linewidth=0.5)

# Lojas existentes
plt.scatter(df.loc[mask_existing, 'lon'], df.loc[mask_existing, 'lat'], 
           s=df.loc[mask_existing, 'population']/8000, alpha=0.8, 
           c='red', marker='s', label='Lojas existentes', edgecolors='darkred', linewidth=1)

# Destacar TOP 5 com maior potencial
plt.scatter(top5_cities['lon'], top5_cities['lat'], 
           s=top5_cities['population']/5000, alpha=0.9, 
           c='gold', marker='*', label='TOP 5 Potencial', 
           edgecolors='orange', linewidth=2)

# Anotar TOP 5
for i, (_, city) in enumerate(top5_cities.iterrows(), 1):
    plt.annotate(f"{i}º {city['city']}", 
                (city['lon'], city['lat']),
                xytext=(10, 10), textcoords='offset points',
                fontsize=10, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='gold', alpha=0.8),
                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

# Anotar lojas existentes
for _, store in existing_stores.iterrows():
    plt.annotate(store['city'], (store['lon'], store['lat']),
                xytext=(5, 5), textcoords='offset points', fontsize=8, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='pink', alpha=0.7))

plt.title('Mapa de Potencial de Vendas - Estado de São Paulo\n(Tamanho dos pontos = População)', 
         fontsize=16, fontweight='bold')
plt.xlabel('Longitude', fontsize=12)
plt.ylabel('Latitude', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.3)
plt.legend(loc='upper right', fontsize=10)
plt.tight_layout()
plt.show()

# Métricas Detalhadas das Top 5 Cidades

print("📊 ANÁLISE DETALHADA DAS TOP 5 CIDADES")
print("=" * 70)

total_population_top5 = top5_cities['population'].sum()
total_regional_pop = top5_cities['regional_population'].sum()
avg_distance = top5_cities['min_dist_existing'].mean()

print(f"📈 População Total das Top 5: {total_population_top5:,} habitantes")
print(f"🌍 População Regional Total (50km): {total_regional_pop:,} habitantes")
print(f"📏 Distância Média de Lojas Existentes: {avg_distance:.1f} km")
print(f"💰 Potencial de Mercado Estimado: {total_population_top5 * 0.15:,.0f} clientes potenciais")
print(f"   (assumindo 15% de penetração de mercado)")

print("\n🎯 RECOMENDAÇÕES ESTRATÉGICAS:")
print("-" * 40)

for i, (_, city) in enumerate(top5_cities.iterrows(), 1):
    market_potential = city['population'] * 0.15
    if city['min_dist_existing'] > 50:
        competition = "Baixa"
    elif city['min_dist_existing'] > 30:
        competition = "Média"
    else:
        competition = "Alta"
    
    print(f"{i}º {city['city']} - {city['state']}:")
    print(f"   • Mercado Potencial: {market_potential:,.0f} clientes")
    print(f"   • Competição Local: {competition}")
    print(f"   • Prioridade: {'🔥 ALTA' if i <= 2 else '⭐ MÉDIA' if i <= 4 else '✅ BAIXA'}")
    print()

# Tabela Comparativa Completa

# Criar tabela com todas as métricas importantes
comparison_df = top5_cities[[
    'city', 'state', 'population', 'min_dist_existing', 
    'regional_population', 'population_score', 'distance_score', 
    'regional_density_score', 'sales_potential_score'
]].copy()

comparison_df.columns = [
    'Cidade', 'Estado', 'População', 'Dist_Loja_Próxima_km', 
    'Pop_Regional_50km', 'Score_População', 'Score_Distância', 
    'Score_Densidade', 'Score_Final'
]

# Adicionar ranking
comparison_df.insert(0, 'Ranking', range(1, 6))

# Formatar números
comparison_df['População'] = comparison_df['População'].apply(lambda x: f"{x:,}")
comparison_df['Pop_Regional_50km'] = comparison_df['Pop_Regional_50km'].apply(lambda x: f"{x:,}")
comparison_df['Dist_Loja_Próxima_km'] = comparison_df['Dist_Loja_Próxima_km'].round(1)

# Arredondar scores
score_cols = ['Score_População', 'Score_Distância', 'Score_Densidade', 'Score_Final']
for col in score_cols:
    comparison_df[col] = comparison_df[col].round(3)

print("📋 TABELA COMPARATIVA COMPLETA - TOP 5 CIDADES")
print("=" * 80)
display(comparison_df)

# Salvar resultado em CSV
comparison_df.to_csv('top5_cidades_potencial_vendas.csv', index=False, encoding='utf-8')
print("\n💾 Resultado salvo em: top5_cidades_potencial_vendas.csv")

# Explicação da Metodologia

print("🔬 METODOLOGIA DE ANÁLISE")
print("=" * 50)
print()
print("A análise de potencial de vendas foi baseada em 3 fatores principais:")
print()
print("1️⃣ SCORE DE POPULAÇÃO (40% do peso):")
print("   • Maior população = maior mercado consumidor potencial")
print("   • Normalizado entre 0-1 baseado na população mínima e máxima")
print()
print("2️⃣ SCORE DE DISTÂNCIA (30% do peso):")
print("   • Maior distância de lojas existentes = menor canibalização")
print("   • Evita competição direta com lojas próprias")
print("   • Garante cobertura geográfica adequada")
print()
print("3️⃣ SCORE DE DENSIDADE REGIONAL (30% do peso):")
print("   • População total em um raio de 50km")
print("   • Considera o mercado regional ampliado")
print("   • Importante para logística e distribuição")
print()
print("🧮 FÓRMULA FINAL:")
print("Score = 0.40×Score_Pop + 0.30×Score_Dist + 0.30×Score_Densidade")
print()
print("✅ CRITÉRIOS DE EXCLUSÃO:")
print("   • Apenas cidades SEM lojas existentes foram consideradas")
print("   • Evita canibalização do negócio atual")
print()
print("📊 DADOS UTILIZADOS:")
print(f"   • Total de cidades analisadas: {len(df)}")
print(f"   • Cidades com lojas existentes: {df['existing_store'].sum()}")
print(f"   • Cidades candidatas: {len(df_candidates)}")
print(f"   • Estado analisado: São Paulo")

# Varredura de k para CDs (lojas existentes)
ks = list(range(2, 7))
X_exist = df_existing[['lat','lon']].values
inertias_cd, sil_cd = [], []
for k in ks:
    km = KMeans(n_clusters=k, n_init='auto', random_state=42)
    labels = km.fit_predict(X_exist)
    inertias_cd.append(km.inertia_)
    sil_cd.append(silhouette_score(X_exist, labels))

fig, ax = plt.subplots(figsize=(7,4.5))
ax.plot(ks, inertias_cd, marker='o')
ax.set_title("CDs · Curva do Cotovelo (Inércia)")
ax.set_xlabel("k"); ax.set_ylabel("Inércia (WCSS)")
ax.grid(True, linestyle='--', alpha=0.3)
plt.show()

fig, ax = plt.subplots(figsize=(7,4.5))
ax.plot(ks, sil_cd, marker='o')
ax.set_title("CDs · Silhouette por k")
ax.set_xlabel("k"); ax.set_ylabel("Silhouette")
ax.grid(True, linestyle='--', alpha=0.3)
plt.show()


# Varredura de k para Expansão (ponderado por população)
ks = list(range(2, 7))
X_full = df[['lat','lon']].values
w = df['population'].values.astype(float)
inertias_exp, sil_exp = [], []
for k in ks:
    km = KMeans(n_clusters=k, n_init='auto', random_state=42)
    labels = km.fit_predict(X_full, sample_weight=w)
    inertias_exp.append(km.inertia_)
    # Silhouette sem peso (aprox.)
    from sklearn.metrics import silhouette_score
    sil_exp.append(silhouette_score(X_full, labels))

fig, ax = plt.subplots(figsize=(7,4.5))
ax.plot(ks, inertias_exp, marker='o')
ax.set_title("Expansão · Curva do Cotovelo (Inércia)")
ax.set_xlabel("k"); ax.set_ylabel("Inércia (WCSS)")
ax.grid(True, linestyle='--', alpha=0.3)
plt.show()

fig, ax = plt.subplots(figsize=(7,4.5))
ax.plot(ks, sil_exp, marker='o')
ax.set_title("Expansão · Silhouette por k (aprox.)")
ax.set_xlabel("k"); ax.set_ylabel("Silhouette")
ax.grid(True, linestyle='--', alpha=0.3)
plt.show()
