{"cells": [{"cell_type": "code", "execution_count": null, "id": "29c4f2f4", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.cluster import KMeans\n", "from sklearn.metrics import silhouette_score\n", "\n", "# Distância Haversine (km)\n", "def haversine(lat1, lon1, lat2, lon2):\n", "    R = 6371.0\n", "    p1 = np.radians(lat1); p2 = np.radians(lat2)\n", "    dlat = np.radians(lat2 - lat1)\n", "    dlon = np.radians(lon2 - lon1)\n", "    a = np.sin(dlat/2)**2 + np.cos(p1)*np.cos(p2)*np.sin(dlon/2)**2\n", "    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))\n", "    return R * c\n", "\n", "def nearest_city(centroid, df, exclude_existing=False):\n", "    lat, lon = centroid\n", "    dists = haversine(lat, lon, df['lat'].values, df['lon'].values)\n", "    order = np.argsort(dists)\n", "    if not exclude_existing:\n", "        idx = int(order[0])\n", "        row = df.iloc[idx]\n", "        return row['city'], row['state'], float(dists[order[0]])\n", "    # procurar a mais próxima que NÃO seja loja existente\n", "    for idx in order:\n", "        row = df.iloc[int(idx)]\n", "        if not row.get('existing_store', False):\n", "            return row['city'], row['state'], float(dists[int(idx)])\n", "    # fallback: se todas forem existentes, retorna a mais próxima mesmo assim\n", "    idx = int(order[0])\n", "    row = df.iloc[idx]\n", "    return row['city'], row['state'], float(dists[idx])\n"]}, {"cell_type": "code", "execution_count": null, "id": "29f47504", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["sp_data = [\n", "    {'city': 'São Paulo', 'state': 'SP', 'lat': -23.5505, 'lon': -46.6333, 'population': 12000000, 'existing_store': True},\n", "    {'city': 'Guarul<PERSON>', 'state': 'SP', 'lat': -23.4543, 'lon': -46.5337, 'population': 1400000, 'existing_store': False},\n", "    {'city': 'Osasco', 'state': 'SP', 'lat': -23.532, 'lon': -46.7913, 'population': 700000, 'existing_store': False},\n", "    {'city': 'Santo André', 'state': 'SP', 'lat': -23.6637, 'lon': -46.5383, 'population': 720000, 'existing_store': False},\n", "    {'city': 'São Bernardo do Campo', 'state': 'SP', 'lat': -23.689, 'lon': -46.5646, 'population': 840000, 'existing_store': False},\n", "    {'city': '<PERSON>adema', 'state': 'SP', 'lat': -23.6813, 'lon': -46.62, 'population': 420000, 'existing_store': False},\n", "    {'city': 'Barueri', 'state': 'SP', 'lat': -23.5057, 'lon': -46.879, 'population': 280000, 'existing_store': False},\n", "    {'city': 'Jundiaí', 'state': 'SP', 'lat': -23.1857, 'lon': -46.8978, 'population': 440000, 'existing_store': True},\n", "    {'city': 'Campinas', 'state': 'SP', 'lat': -22.9056, 'lon': -47.0608, 'population': 1300000, 'existing_store': True},\n", "    {'city': 'Americana', 'state': 'SP', 'lat': -22.7377, 'lon': -47.3331, 'population': 240000, 'existing_store': False},\n", "    {'city': 'Limeira', 'state': 'SP', 'lat': -22.565, 'lon': -47.4009, 'population': 310000, 'existing_store': False},\n", "    {'city': 'Piracicaba', 'state': 'SP', 'lat': -22.7338, 'lon': -47.6476, 'population': 410000, 'existing_store': False},\n", "    {'city': 'Rio Claro', 'state': 'SP', 'lat': -22.4111, 'lon': -47.561, 'population': 210000, 'existing_store': False},\n", "    {'city': 'Sorocaba', 'state': 'SP', 'lat': -23.5015, 'lon': -47.4526, 'population': 700000, 'existing_store': True},\n", "    {'city': 'Indaiatuba', 'state': 'SP', 'lat': -23.0907, 'lon': -47.2181, 'population': 270000, 'existing_store': False},\n", "    {'city': 'Itu', 'state': 'SP', 'lat': -23.2665, 'lon': -47.2992, 'population': 180000, 'existing_store': False},\n", "    {'city': 'Itapetininga', 'state': 'SP', 'lat': -23.5917, 'lon': -48.053, 'population': 170000, 'existing_store': False},\n", "    {'city': 'Tatuí', 'state': 'SP', 'lat': -23.3556, 'lon': -47.8569, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Botucatu', 'state': 'SP', 'lat': -22.8859, 'lon': -48.4446, 'population': 150000, 'existing_store': False},\n", "    {'city': 'Bauru', 'state': 'SP', 'lat': -22.3246, 'lon': -49.0871, 'population': 380000, 'existing_store': True},\n", "    {'city': 'Ja<PERSON>', 'state': 'SP', 'lat': -22.2966, 'lon': -48.5583, 'population': 150000, 'existing_store': False},\n", "    {'city': 'Marília', 'state': 'SP', 'lat': -22.2171, 'lon': -49.9501, 'population': 230000, 'existing_store': False},\n", "    {'city': 'Ourinhos', 'state': 'SP', 'lat': -22.9797, 'lon': -49.8719, 'population': 115000, 'existing_store': False},\n", "    {'city': 'Assis', 'state': 'SP', 'lat': -22.6619, 'lon': -50.4121, 'population': 105000, 'existing_store': False},\n", "    {'city': 'President<PERSON>', 'state': 'SP', 'lat': -22.1207, 'lon': -51.3925, 'population': 230000, 'existing_store': False},\n", "    {'city': 'Araçatuba', 'state': 'SP', 'lat': -21.206, 'lon': -50.432, 'population': 200000, 'existing_store': False},\n", "    {'city': 'Birigui', 'state': 'SP', 'lat': -21.2887, 'lon': -50.3432, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Penápolis', 'state': 'SP', 'lat': -21.419, 'lon': -50.0773, 'population': 65000, 'existing_store': False},\n", "    {'city': 'Andradina', 'state': 'SP', 'lat': -20.8967, 'lon': -51.3795, 'population': 57000, 'existing_store': False},\n", "    {'city': 'São José do Rio Preto', 'state': 'SP', 'lat': -20.8113, 'lon': -49.3758, 'population': 470000, 'existing_store': True},\n", "    {'city': 'Catanduva', 'state': 'SP', 'lat': -21.1316, 'lon': -48.977, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Barretos', 'state': 'SP', 'lat': -20.5531, 'lon': -48.567, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Franca', 'state': 'SP', 'lat': -20.5386, 'lon': -47.4004, 'population': 370000, 'existing_store': False},\n", "    {'city': 'Ribeirão Preto', 'state': 'SP', 'lat': -21.1775, 'lon': -47.8103, 'population': 720000, 'existing_store': True},\n", "    {'city': 'São Carlos', 'state': 'SP', 'lat': -22.0087, 'lon': -47.8976, 'population': 250000, 'existing_store': False},\n", "    {'city': 'Araraquara', 'state': 'SP', 'lat': -21.7845, 'lon': -48.178, 'population': 240000, 'existing_store': False},\n", "    {'city': '<PERSON>gi das Cruzes', 'state': 'SP', 'lat': -23.5208, 'lon': -46.1855, 'population': 460000, 'existing_store': False},\n", "    {'city': 'Suzano', 'state': 'SP', 'lat': -23.5439, 'lon': -46.311, 'population': 300000, 'existing_store': False},\n", "    {'city': 'Bragança Paulista', 'state': 'SP', 'lat': -22.9527, 'lon': -46.5419, 'population': 180000, 'existing_store': False},\n", "    {'city': 'Atibaia', 'state': 'SP', 'lat': -23.1171, 'lon': -46.5548, 'population': 150000, 'existing_store': False},\n", "    {'city': 'Jacareí', 'state': 'SP', 'lat': -23.2983, 'lon': -45.9658, 'population': 240000, 'existing_store': False},\n", "    {'city': 'São José dos Campos', 'state': 'SP', 'lat': -23.1896, 'lon': -45.8841, 'population': 750000, 'existing_store': True},\n", "    {'city': 'Taubaté', 'state': 'SP', 'lat': -23.0205, 'lon': -45.5569, 'population': 320000, 'existing_store': False},\n", "    {'city': 'Pindamonhangaba', 'state': 'SP', 'lat': -22.9235, 'lon': -45.4614, 'population': 170000, 'existing_store': False},\n", "    {'city': 'Santos', 'state': 'SP', 'lat': -23.959, 'lon': -46.3336, 'population': 430000, 'existing_store': True},\n", "    {'city': 'São Vicente', 'state': 'SP', 'lat': -23.9631, 'lon': -46.3919, 'population': 370000, 'existing_store': False},\n", "    {'city': 'Praia Grande', 'state': 'SP', 'lat': -24.0058, 'lon': -46.402, 'population': 330000, 'existing_store': False}\n", "]\n", "df = pd.DataFrame(sp_data)\n", "print(\"Total de cidades:\", len(df))\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "31fb2f22", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["plt.figure(figsize=(9,6))\n", "mask = df['existing_store']\n", "plt.scatter(df.loc[~mask, 'lon'], df.loc[~mask, 'lat'], s=25, alpha=0.8, label='Cidades sem loja')\n", "plt.scatter(df.loc[mask, 'lon'], df.loc[mask, 'lat'], s=60, alpha=0.9, marker='s', label='Lojas existentes')\n", "for _, row in df.iterrows():\n", "    if row['existing_store']:\n", "        plt.annotate(row['city'], (row['lon'], row['lat']), fontsize=7, alpha=0.8)\n", "plt.title(\"Cidades de SP · Lojas existentes destacadas\")\n", "plt.xlabel(\"Longitude\"); plt.ylabel(\"Latitude\")\n", "plt.grid(True, linestyle='--', alpha=0.3); plt.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "1d22bb4d", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["df_existing = df[df['existing_store']].copy()\n", "print(\"Lojas existentes:\", len(df_existing))\n", "df_existing[['city','state','lat','lon']].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "31ae4dfe", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def run_cd_scenario(k, df_existing):\n", "    X = df_existing[['lat','lon']].values\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X)\n", "    centroids = km.cluster_centers_\n", "    inertia = km.inertia_\n", "    sil = silhouette_score(X, labels) if k > 1 and k < len(X) else float('nan')\n", "\n", "    # Plot clusters (apenas lojas existentes)\n", "    plt.figure(figsize=(9,6))\n", "    for i in range(k):\n", "        pts = X[labels == i]\n", "        plt.scatter(pts[:,1], pts[:,0], s=45, alpha=0.9, label=f\"Cluster {i+1}\")\n", "    plt.scatter(centroids[:,1], centroids[:,0], s=200, marker='X', edgecolor='black', linewidth=1, label=\"Centróides (CDs)\")\n", "    plt.title(f\"CDs para lojas existentes — k={k}\")\n", "    plt.xlabel(\"Longitude\"); plt.ylabel(\"Latitude\")\n", "    plt.grid(True, linestyle='--', alpha=0.3); plt.legend()\n", "    plt.show()\n", "\n", "    # Cidades candidatas aos CDs (mais próximas dos centróides)\n", "    candidatos = []\n", "    for c in centroids:\n", "        cidade, estado, dist_km = nearest_city(c, df_existing, exclude_existing=False)\n", "        candidatos.append((cidade, estado, dist_km, c[0], c[1]))\n", "    out = pd.DataFrame(candidatos, columns=[\"cidade_candidata\",\"estado\",\"dist_km\",\"centroid_lat\",\"centroid_lon\"])\n", "    display(out.sort_values(\"dist_km\"))\n", "    print(f\"Inércia (WCSS): {inertia:.2f} | Silhouette: {sil:.3f}\")\n", "    return centroids, labels, inertia, sil\n"]}, {"cell_type": "code", "execution_count": null, "id": "45463e46", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cA1 = run_cd_scenario(1, df_existing)"]}, {"cell_type": "code", "execution_count": null, "id": "213370da", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cA2 = run_cd_scenario(2, df_existing)"]}, {"cell_type": "code", "execution_count": null, "id": "aababd70", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cA3 = run_cd_scenario(3, df_existing)"]}, {"cell_type": "code", "execution_count": null, "id": "74477b97", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def run_weighted_expansion(k, df_full):\n", "    X = df_full[['lat','lon']].values\n", "    w = df_full['population'].values.astype(float)\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X, sample_weight=w)\n", "    centroids = km.cluster_centers_\n", "    inertia = km.inertia_\n", "\n", "    # silhouette sem peso (aprox.) — apenas para referência\n", "    sil = silhouette_score(X, labels) if k > 1 and k < len(X) else float('nan')\n", "\n", "    # Plot: cidades sem loja (oportunidade) vs existentes\n", "    plt.figure(figsize=(9,6))\n", "    mask = df_full['existing_store']\n", "    plt.scatter(df_full.loc[~mask,'lon'], df_full.loc[~mask,'lat'], s=20, alpha=0.8, label=\"Cidades sem loja\")\n", "    plt.scatter(df_full.loc[mask,'lon'], df_full.loc[mask,'lat'], s=60, alpha=0.9, marker='s', label=\"Lojas existentes\")\n", "    plt.scatter(centroids[:,1], centroids[:,0], s=220, marker='X', edgecolor='black', linewidth=1, label=\"Centróides (demanda)\")\n", "    plt.title(f\"Clusters de demanda (população) — k={k}\")\n", "    plt.xlabel(\"Longitude\"); plt.ylabel(\"Latitude\")\n", "    plt.grid(True, linestyle='--', alpha=0.3); plt.legend()\n", "    plt.show()\n", "\n", "    # Sugerir cidades candidatas (preferindo não existentes)\n", "    candidatos = []\n", "    for c in centroids:\n", "        cidade, estado, dist_km = nearest_city(c, df_full, exclude_existing=True)\n", "        candidatos.append((cidade, estado, dist_km, c[0], c[1]))\n", "    out = pd.DataFrame(candidatos, columns=[\"cidade_candidata_nova_loja\",\"estado\",\"dist_km\",\"centroid_lat\",\"centroid_lon\"])\n", "    display(out.sort_values(\"dist_km\"))\n", "    print(f\"Inércia (WCSS): {inertia:.2f} | Silhouette (aprox. sem peso): {sil:.3f}\")\n", "    return centroids, labels, inertia, sil"]}, {"cell_type": "code", "execution_count": null, "id": "715d54ca", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cB1 = run_weighted_expansion(1, df)"]}, {"cell_type": "code", "execution_count": null, "id": "692d2004", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cB2 = run_weighted_expansion(2, df)"]}, {"cell_type": "code", "execution_count": null, "id": "d1c9d90a", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cB3 = run_weighted_expansion(3, df)"]}, {"cell_type": "markdown", "id": "analysis_header", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON><PERSON> das 5 Cidades com Maior Potencial de Vendas\n", "\n", "Esta análise considera múltiplos fatores para determinar o potencial de vendas:\n", "- **População**: <PERSON><PERSON> pop<PERSON> = maior mercado potencial\n", "- **Ausência de loja existente**: <PERSON><PERSON><PERSON>\n", "- **Distância de lojas existentes**: Garantir cobertura geográfica adequada\n", "- **Densidade populacional**: Concentração de clientes potenciais"]}, {"cell_type": "code", "execution_count": null, "id": "potential_analysis", "metadata": {}, "outputs": [], "source": ["# Análise de Potencial de Vendas\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Filtrar apenas cidades sem lojas existentes\n", "df_candidates = df[~df['existing_store']].copy()\n", "\n", "print(f\"Total de cidades candidatas (sem loja existente): {len(df_candidates)}\")\n", "print(f\"Total de lojas existentes: {df['existing_store'].sum()}\")\n", "\n", "# Calcular distância mínima para lojas existentes\n", "existing_stores = df[df['existing_store']]\n", "\n", "def min_distance_to_existing_store(row, existing_stores):\n", "    \"\"\"Calcula a distância mínima de uma cidade para qualquer loja existente\"\"\"\n", "    distances = []\n", "    for _, store in existing_stores.iterrows():\n", "        dist = haversine(row['lat'], row['lon'], store['lat'], store['lon'])\n", "        distances.append(dist)\n", "    return min(distances) if distances else float('inf')\n", "\n", "# Adicionar coluna de distância mínima para lojas existentes\n", "df_candidates['min_dist_existing'] = df_candidates.apply(\n", "    lambda row: min_distance_to_existing_store(row, existing_stores), axis=1\n", ")\n", "\n", "df_candidates.head()"]}, {"cell_type": "code", "execution_count": null, "id": "scoring_algorithm", "metadata": {}, "outputs": [], "source": ["# Algoritmo de Pontuação para Potencial de Vendas\n", "\n", "def calculate_sales_potential_score(df_candidates):\n", "    \"\"\"Calcula score de potencial de vendas baseado em múltiplos fatores\"\"\"\n", "    df_score = df_candidates.copy()\n", "    \n", "    # 1. Score de População (40% do peso total)\n", "    # Normalizar população entre 0-1 e aplicar peso\n", "    pop_min, pop_max = df_score['population'].min(), df_score['population'].max()\n", "    df_score['population_score'] = (df_score['population'] - pop_min) / (pop_max - pop_min)\n", "    \n", "    # 2. Score de Distância (30% do peso total)\n", "    # Maior distância de lojas existentes = melhor (evita canibaliza<PERSON>)\n", "    dist_min, dist_max = df_score['min_dist_existing'].min(), df_score['min_dist_existing'].max()\n", "    df_score['distance_score'] = (df_score['min_dist_existing'] - dist_min) / (dist_max - dist_min)\n", "    \n", "    # 3. Score de Densidade Regional (30% do peso total)\n", "    # Calcular densidade populacional em um raio de 50km\n", "    def regional_density(row, df_all):\n", "        total_pop = 0\n", "        for _, other in df_all.iterrows():\n", "            dist = haversine(row['lat'], row['lon'], other['lat'], other['lon'])\n", "            if dist <= 50:  # raio de 50km\n", "                total_pop += other['population']\n", "        return total_pop\n", "    \n", "    df_score['regional_population'] = df_score.apply(\n", "        lambda row: regional_density(row, df), axis=1\n", "    )\n", "    \n", "    reg_min, reg_max = df_score['regional_population'].min(), df_score['regional_population'].max()\n", "    df_score['regional_density_score'] = (df_score['regional_population'] - reg_min) / (reg_max - reg_min)\n", "    \n", "    # Score Final Ponderado\n", "    df_score['sales_potential_score'] = (\n", "        0.40 * df_score['population_score'] +\n", "        0.30 * df_score['distance_score'] +\n", "        0.30 * df_score['regional_density_score']\n", "    )\n", "    \n", "    return df_score\n", "\n", "# Calcular scores\n", "df_scored = calculate_sales_potential_score(df_candidates)\n", "\n", "# Mostrar estatísticas dos scores\n", "print(\"Estatísticas dos Scores:\")\n", "print(df_scored[['population_score', 'distance_score', 'regional_density_score', 'sales_potential_score']].describe())"]}, {"cell_type": "code", "execution_count": null, "id": "top5_cities", "metadata": {}, "outputs": [], "source": ["# TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS\n", "\n", "top5_cities = df_scored.nlargest(5, 'sales_potential_score')\n", "\n", "print(\"🏆 TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS NO BRASIL\")\n", "print(\"=\" * 60)\n", "\n", "for i, (_, city) in enumerate(top5_cities.iterrows(), 1):\n", "    print(f\"{i}º LUGAR: {city['city']} - {city['state']}\")\n", "    print(f\"   📊 Score de Potencial: {city['sales_potential_score']:.3f}\")\n", "    print(f\"   👥 População: {city['population']:,} habitantes\")\n", "    print(f\"   📍 Distância da loja mais próxima: {city['min_dist_existing']:.1f} km\")\n", "    print(f\"   🏘️  População regional (50km): {city['regional_population']:,} habitantes\")\n", "    print()\n", "\n", "# Criar DataFrame resumo para visualização\n", "summary_df = top5_cities[['city', 'state', 'population', 'min_dist_existing', \n", "                         'regional_population', 'sales_potential_score']].copy()\n", "summary_df.columns = ['Cidade', 'Estado', 'População', 'Dist_Loja_Próxima_km', \n", "                     'Pop_Regional_50km', 'Score_Potencial']\n", "summary_df = summary_df.round(3)\n", "\n", "print(\"\\n📋 RESUMO EXECUTIVO - TOP 5:\")\n", "display(summary_df)"]}, {"cell_type": "code", "execution_count": null, "id": "visualization_analysis", "metadata": {}, "outputs": [], "source": ["# Visualizações da Análise\n", "\n", "# 1. <PERSON><PERSON><PERSON><PERSON><PERSON> barras - Top 5 cidades por score\n", "plt.figure(figsize=(12, 6))\n", "plt.subplot(1, 2, 1)\n", "cities_names = [f\"{city['city']}\\n{city['state']}\" for _, city in top5_cities.iterrows()]\n", "scores = top5_cities['sales_potential_score'].values\n", "colors = plt.cm.viridis(np.linspace(0, 1, 5))\n", "\n", "bars = plt.bar(range(5), scores, color=colors)\n", "plt.title('Top 5 Cidades - Score de Potencial de Vendas', fontsize=14, fontweight='bold')\n", "plt.xlabel('Cidades')\n", "plt.ylabel('Score de Potencial')\n", "plt.xticks(range(5), cities_names, rotation=45, ha='right')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Adicionar valores nas barras\n", "for i, bar in enumerate(bars):\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "             f'{height:.3f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 2. <PERSON><PERSON><PERSON> plot - <PERSON><PERSON><PERSON> vs Distância\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(df_candidates['population'], df_candidates['min_dist_existing'], \n", "           c=df_candidates['sales_potential_score'], cmap='viridis', \n", "           s=60, alpha=0.7)\n", "plt.colorbar(label='Score de Potencial')\n", "\n", "# Destacar top 5\n", "for _, city in top5_cities.iterrows():\n", "    plt.scatter(city['population'], city['min_dist_existing'], \n", "               s=200, facecolors='none', edgecolors='red', linewidth=2)\n", "    plt.annotate(city['city'], (city['population'], city['min_dist_existing']),\n", "                xytext=(5, 5), textcoords='offset points', fontsize=8, \n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))\n", "\n", "plt.title('População vs Distância de Lojas Existentes', fontsize=14, fontweight='bold')\n", "plt.xlabel('População')\n", "plt.ylabel('Distância da Loja Mais Próxima (km)')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "geographic_visualization", "metadata": {}, "outputs": [], "source": ["# Mapa Geográfico com Top 5 Destacado\n", "\n", "plt.figure(figsize=(12, 8))\n", "\n", "# <PERSON><PERSON><PERSON> todas as cidades\n", "mask_existing = df['existing_store']\n", "mask_candidates = ~df['existing_store']\n", "\n", "# Cidades sem loja (candidatas)\n", "plt.scatter(df.loc[mask_candidates, 'lon'], df.loc[mask_candidates, 'lat'], \n", "           s=df.loc[mask_candidates, 'population']/10000, alpha=0.6, \n", "           c='lightblue', label='Cidades sem loja', edgecolors='gray', linewidth=0.5)\n", "\n", "# Lojas existentes\n", "plt.scatter(df.loc[mask_existing, 'lon'], df.loc[mask_existing, 'lat'], \n", "           s=df.loc[mask_existing, 'population']/8000, alpha=0.8, \n", "           c='red', marker='s', label='Lojas existentes', edgecolors='darkred', linewidth=1)\n", "\n", "# Destacar TOP 5 com maior potencial\n", "plt.scatter(top5_cities['lon'], top5_cities['lat'], \n", "           s=top5_cities['population']/5000, alpha=0.9, \n", "           c='gold', marker='*', label='TOP 5 Potencial', \n", "           edgecolors='orange', linewidth=2)\n", "\n", "# Anotar TOP 5\n", "for i, (_, city) in enumerate(top5_cities.iterrows(), 1):\n", "    plt.annotate(f\"{i}º {city['city']}\", \n", "                (city['lon'], city['lat']),\n", "                xytext=(10, 10), textcoords='offset points',\n", "                fontsize=10, fontweight='bold',\n", "                bbox=dict(boxstyle='round,pad=0.5', facecolor='gold', alpha=0.8),\n", "                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))\n", "\n", "# Anotar lojas existentes\n", "for _, store in existing_stores.iterrows():\n", "    plt.annotate(store['city'], (store['lon'], store['lat']),\n", "                xytext=(5, 5), textcoords='offset points', fontsize=8, \n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='pink', alpha=0.7))\n", "\n", "plt.title('Mapa de Potencial de Vendas - Estado de São Paulo\\n(Tamanho dos pontos = População)', \n", "         fontsize=16, fontweight='bold')\n", "plt.xlabel('Longitude', fontsize=12)\n", "plt.ylabel('Latitude', fontsize=12)\n", "plt.grid(True, linestyle='--', alpha=0.3)\n", "plt.legend(loc='upper right', fontsize=10)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "detailed_metrics", "metadata": {}, "outputs": [], "source": ["# Métricas Detalhadas das Top 5 Cidades\n", "\n", "print(\"📊 ANÁLISE DETALHADA DAS TOP 5 CIDADES\")\n", "print(\"=\" * 70)\n", "\n", "total_population_top5 = top5_cities['population'].sum()\n", "total_regional_pop = top5_cities['regional_population'].sum()\n", "avg_distance = top5_cities['min_dist_existing'].mean()\n", "\n", "print(f\"📈 População Total das Top 5: {total_population_top5:,} habitantes\")\n", "print(f\"🌍 População Regional Total (50km): {total_regional_pop:,} habitantes\")\n", "print(f\"📏 Distância Média de Lojas Existentes: {avg_distance:.1f} km\")\n", "print(f\"💰 Potencial de Mercado Estimado: {total_population_top5 * 0.15:,.0f} clientes potenciais\")\n", "print(f\"   (assumindo 15% de penetração de mercado)\")\n", "\n", "print(\"\\n🎯 RECOMENDAÇÕES ESTRATÉGICAS:\")\n", "print(\"-\" * 40)\n", "\n", "for i, (_, city) in enumerate(top5_cities.iterrows(), 1):\n", "    market_potential = city['population'] * 0.15\n", "    if city['min_dist_existing'] > 50:\n", "        competition = \"Baixa\"\n", "    elif city['min_dist_existing'] > 30:\n", "        competition = \"Média\"\n", "    else:\n", "        competition = \"Alta\"\n", "    \n", "    print(f\"{i}º {city['city']} - {city['state']}:\")\n", "    print(f\"   • Mercado Potencial: {market_potential:,.0f} clientes\")\n", "    print(f\"   • Competição Local: {competition}\")\n", "    print(f\"   • Prioridade: {'🔥 ALTA' if i <= 2 else '⭐ MÉDIA' if i <= 4 else '✅ BAIXA'}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "id": "comparison_table", "metadata": {}, "outputs": [], "source": ["# Tabela Comparativa Completa\n", "\n", "# Criar tabela com todas as métricas importantes\n", "comparison_df = top5_cities[[\n", "    'city', 'state', 'population', 'min_dist_existing', \n", "    'regional_population', 'population_score', 'distance_score', \n", "    'regional_density_score', 'sales_potential_score'\n", "]].copy()\n", "\n", "comparison_df.columns = [\n", "    'Cidade', '<PERSON>stad<PERSON>', 'População', 'Dist_Loja_Próxima_km', \n", "    'Pop_Regional_50km', 'Score_População', 'Score_Distância', \n", "    'Score_Densidade', 'Score_Final'\n", "]\n", "\n", "# Adicionar ranking\n", "comparison_df.insert(0, 'Ranking', range(1, 6))\n", "\n", "# Formatar números\n", "comparison_df['População'] = comparison_df['População'].apply(lambda x: f\"{x:,}\")\n", "comparison_df['Pop_Regional_50km'] = comparison_df['Pop_Regional_50km'].apply(lambda x: f\"{x:,}\")\n", "comparison_df['Dist_Loja_Próxima_km'] = comparison_df['Dist_Loja_Próxima_km'].round(1)\n", "\n", "# Arredondar scores\n", "score_cols = ['Score_População', 'Score_Distância', 'Score_Densidade', 'Score_Final']\n", "for col in score_cols:\n", "    comparison_df[col] = comparison_df[col].round(3)\n", "\n", "print(\"📋 TABELA COMPARATIVA COMPLETA - TOP 5 CIDADES\")\n", "print(\"=\" * 80)\n", "display(comparison_df)\n", "\n", "# Salvar resultado em CSV\n", "comparison_df.to_csv('top5_cidades_potencial_vendas.csv', index=False, encoding='utf-8')\n", "print(\"\\n💾 Resultado salvo em: top5_cidades_potencial_vendas.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "methodology_explanation", "metadata": {}, "outputs": [], "source": ["# Explicação da Metodologia\n", "\n", "print(\"🔬 METODOLOGIA DE ANÁLISE\")\n", "print(\"=\" * 50)\n", "print()\n", "print(\"A análise de potencial de vendas foi baseada em 3 fatores principais:\")\n", "print()\n", "print(\"1️⃣ SCORE DE POPULAÇÃO (40% do peso):\")\n", "print(\"   • <PERSON><PERSON> popula<PERSON> = maior mercado consumidor potencial\")\n", "print(\"   • Normalizado entre 0-1 baseado na população mínima e máxima\")\n", "print()\n", "print(\"2️⃣ SCORE DE DISTÂNCIA (30% do peso):\")\n", "print(\"   • <PERSON><PERSON> distância de lojas existentes = menor canibalização\")\n", "print(\"   • Evita competição direta com lojas próprias\")\n", "print(\"   • Garante cobertura geográfica adequada\")\n", "print()\n", "print(\"3️⃣ SCORE DE DENSIDADE REGIONAL (30% do peso):\")\n", "print(\"   • População total em um raio de 50km\")\n", "print(\"   • Considera o mercado regional ampliado\")\n", "print(\"   • Importante para logística e distribuição\")\n", "print()\n", "print(\"🧮 FÓRMULA FINAL:\")\n", "print(\"Score = 0.40×Score_Pop + 0.30×Score_Dist + 0.30×Score_Densidade\")\n", "print()\n", "print(\"✅ CRITÉRIOS DE EXCLUSÃO:\")\n", "print(\"   • Apenas cidades SEM lojas existentes foram consideradas\")\n", "print(\"   • Evita canibalização do negócio atual\")\n", "print()\n", "print(\"📊 DADOS UTILIZADOS:\")\n", "print(f\"   • Total de cidades analisadas: {len(df)}\")\n", "print(f\"   • Cidades com lojas existentes: {df['existing_store'].sum()}\")\n", "print(f\"   • Cidades candidatas: {len(df_candidates)}\")\n", "print(f\"   • Estado analisado: São Paulo\")"]}, {"cell_type": "markdown", "id": "analysis_header", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON><PERSON> das 5 Cidades com Maior Potencial de Vendas\n", "\n", "Esta análise considera múltiplos fatores para determinar o potencial de vendas:\n", "- **População**: <PERSON><PERSON> pop<PERSON> = maior mercado potencial\n", "- **Ausência de loja existente**: <PERSON><PERSON><PERSON>\n", "- **Distância de lojas existentes**: Garantir cobertura geográfica adequada\n", "- **Densidade populacional**: Concentração de clientes potenciais"]}, {"cell_type": "code", "execution_count": null, "id": "potential_analysis", "metadata": {}, "outputs": [], "source": ["# Análise de Potencial de Vendas\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Filtrar apenas cidades sem lojas existentes\n", "df_candidates = df[~df['existing_store']].copy()\n", "\n", "print(f\"Total de cidades candidatas (sem loja existente): {len(df_candidates)}\")\n", "print(f\"Total de lojas existentes: {df['existing_store'].sum()}\")\n", "\n", "# Calcular distância mínima para lojas existentes\n", "existing_stores = df[df['existing_store']]\n", "\n", "def min_distance_to_existing_store(row, existing_stores):\n", "    \"\"\"Calcula a distância mínima de uma cidade para qualquer loja existente\"\"\"\n", "    distances = []\n", "    for _, store in existing_stores.iterrows():\n", "        dist = haversine(row['lat'], row['lon'], store['lat'], store['lon'])\n", "        distances.append(dist)\n", "    return min(distances) if distances else float('inf')\n", "\n", "# Adicionar coluna de distância mínima para lojas existentes\n", "df_candidates['min_dist_existing'] = df_candidates.apply(\n", "    lambda row: min_distance_to_existing_store(row, existing_stores), axis=1\n", ")\n", "\n", "df_candidates.head()"]}, {"cell_type": "code", "execution_count": null, "id": "scoring_algorithm", "metadata": {}, "outputs": [], "source": ["# Algoritmo de Pontuação para Potencial de Vendas\n", "\n", "def calculate_sales_potential_score(df_candidates):\n", "    \"\"\"Calcula score de potencial de vendas baseado em múltiplos fatores\"\"\"\n", "    df_score = df_candidates.copy()\n", "    \n", "    # 1. Score de População (40% do peso total)\n", "    # Normalizar população entre 0-1 e aplicar peso\n", "    pop_min, pop_max = df_score['population'].min(), df_score['population'].max()\n", "    df_score['population_score'] = (df_score['population'] - pop_min) / (pop_max - pop_min)\n", "    \n", "    # 2. Score de Distância (30% do peso total)\n", "    # Maior distância de lojas existentes = melhor (evita canibaliza<PERSON>)\n", "    dist_min, dist_max = df_score['min_dist_existing'].min(), df_score['min_dist_existing'].max()\n", "    df_score['distance_score'] = (df_score['min_dist_existing'] - dist_min) / (dist_max - dist_min)\n", "    \n", "    # 3. Score de Densidade Regional (30% do peso total)\n", "    # Calcular densidade populacional em um raio de 50km\n", "    def regional_density(row, df_all):\n", "        total_pop = 0\n", "        for _, other in df_all.iterrows():\n", "            dist = haversine(row['lat'], row['lon'], other['lat'], other['lon'])\n", "            if dist <= 50:  # raio de 50km\n", "                total_pop += other['population']\n", "        return total_pop\n", "    \n", "    df_score['regional_population'] = df_score.apply(\n", "        lambda row: regional_density(row, df), axis=1\n", "    )\n", "    \n", "    reg_min, reg_max = df_score['regional_population'].min(), df_score['regional_population'].max()\n", "    df_score['regional_density_score'] = (df_score['regional_population'] - reg_min) / (reg_max - reg_min)\n", "    \n", "    # Score Final Ponderado\n", "    df_score['sales_potential_score'] = (\n", "        0.40 * df_score['population_score'] +\n", "        0.30 * df_score['distance_score'] +\n", "        0.30 * df_score['regional_density_score']\n", "    )\n", "    \n", "    return df_score\n", "\n", "# Calcular scores\n", "df_scored = calculate_sales_potential_score(df_candidates)\n", "\n", "# Mostrar estatísticas dos scores\n", "print(\"Estatísticas dos Scores:\")\n", "print(df_scored[['population_score', 'distance_score', 'regional_density_score', 'sales_potential_score']].describe())"]}, {"cell_type": "code", "execution_count": null, "id": "top5_cities", "metadata": {}, "outputs": [], "source": ["# TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS\n", "\n", "top5_cities = df_scored.nlargest(5, 'sales_potential_score')\n", "\n", "print(\"🏆 TOP 5 CIDADES COM MAIOR POTENCIAL DE VENDAS NO BRASIL\")\n", "print(\"=\" * 60)\n", "\n", "for i, (_, city) in enumerate(top5_cities.iterrows(), 1):\n", "    print(f\"{i}º LUGAR: {city['city']} - {city['state']}\")\n", "    print(f\"   📊 Score de Potencial: {city['sales_potential_score']:.3f}\")\n", "    print(f\"   👥 População: {city['population']:,} habitantes\")\n", "    print(f\"   📍 Distância da loja mais próxima: {city['min_dist_existing']:.1f} km\")\n", "    print(f\"   🏘️  População regional (50km): {city['regional_population']:,} habitantes\")\n", "    print()\n", "\n", "# Criar DataFrame resumo para visualização\n", "summary_df = top5_cities[['city', 'state', 'population', 'min_dist_existing', \n", "                         'regional_population', 'sales_potential_score']].copy()\n", "summary_df.columns = ['Cidade', 'Estado', 'População', 'Dist_Loja_Próxima_km', \n", "                     'Pop_Regional_50km', 'Score_Potencial']\n", "summary_df = summary_df.round(3)\n", "\n", "print(\"\\n📋 RESUMO EXECUTIVO - TOP 5:\")\n", "display(summary_df)"]}, {"cell_type": "code", "execution_count": null, "id": "visualization_analysis", "metadata": {}, "outputs": [], "source": ["# Visualizações da Análise\n", "\n", "# 1. <PERSON><PERSON><PERSON><PERSON><PERSON> barras - Top 5 cidades por score\n", "plt.figure(figsize=(12, 6))\n", "plt.subplot(1, 2, 1)\n", "cities_names = [f\"{city['city']}\\n{city['state']}\" for _, city in top5_cities.iterrows()]\n", "scores = top5_cities['sales_potential_score'].values\n", "colors = plt.cm.viridis(np.linspace(0, 1, 5))\n", "\n", "bars = plt.bar(range(5), scores, color=colors)\n", "plt.title('Top 5 Cidades - Score de Potencial de Vendas', fontsize=14, fontweight='bold')\n", "plt.xlabel('Cidades')\n", "plt.ylabel('Score de Potencial')\n", "plt.xticks(range(5), cities_names, rotation=45, ha='right')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Adicionar valores nas barras\n", "for i, bar in enumerate(bars):\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "             f'{height:.3f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 2. <PERSON><PERSON><PERSON> plot - <PERSON><PERSON><PERSON> vs Distância\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(df_candidates['population'], df_candidates['min_dist_existing'], \n", "           c=df_candidates['sales_potential_score'], cmap='viridis', \n", "           s=60, alpha=0.7)\n", "plt.colorbar(label='Score de Potencial')\n", "\n", "# Destacar top 5\n", "for _, city in top5_cities.iterrows():\n", "    plt.scatter(city['population'], city['min_dist_existing'], \n", "               s=200, facecolors='none', edgecolors='red', linewidth=2)\n", "    plt.annotate(city['city'], (city['population'], city['min_dist_existing']),\n", "                xytext=(5, 5), textcoords='offset points', fontsize=8, \n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))\n", "\n", "plt.title('População vs Distância de Lojas Existentes', fontsize=14, fontweight='bold')\n", "plt.xlabel('População')\n", "plt.ylabel('Distância da Loja Mais Próxima (km)')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "geographic_visualization", "metadata": {}, "outputs": [], "source": ["# Mapa Geográfico com Top 5 Destacado\n", "\n", "plt.figure(figsize=(12, 8))\n", "\n", "# <PERSON><PERSON><PERSON> todas as cidades\n", "mask_existing = df['existing_store']\n", "mask_candidates = ~df['existing_store']\n", "\n", "# Cidades sem loja (candidatas)\n", "plt.scatter(df.loc[mask_candidates, 'lon'], df.loc[mask_candidates, 'lat'], \n", "           s=df.loc[mask_candidates, 'population']/10000, alpha=0.6, \n", "           c='lightblue', label='Cidades sem loja', edgecolors='gray', linewidth=0.5)\n", "\n", "# Lojas existentes\n", "plt.scatter(df.loc[mask_existing, 'lon'], df.loc[mask_existing, 'lat'], \n", "           s=df.loc[mask_existing, 'population']/8000, alpha=0.8, \n", "           c='red', marker='s', label='Lojas existentes', edgecolors='darkred', linewidth=1)\n", "\n", "# Destacar TOP 5 com maior potencial\n", "plt.scatter(top5_cities['lon'], top5_cities['lat'], \n", "           s=top5_cities['population']/5000, alpha=0.9, \n", "           c='gold', marker='*', label='TOP 5 Potencial', \n", "           edgecolors='orange', linewidth=2)\n", "\n", "# Anotar TOP 5\n", "for i, (_, city) in enumerate(top5_cities.iterrows(), 1):\n", "    plt.annotate(f\"{i}º {city['city']}\", \n", "                (city['lon'], city['lat']),\n", "                xytext=(10, 10), textcoords='offset points',\n", "                fontsize=10, fontweight='bold',\n", "                bbox=dict(boxstyle='round,pad=0.5', facecolor='gold', alpha=0.8),\n", "                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))\n", "\n", "# Anotar lojas existentes\n", "for _, store in existing_stores.iterrows():\n", "    plt.annotate(store['city'], (store['lon'], store['lat']),\n", "                xytext=(5, 5), textcoords='offset points', fontsize=8, \n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='pink', alpha=0.7))\n", "\n", "plt.title('Mapa de Potencial de Vendas - Estado de São Paulo\\n(Tamanho dos pontos = População)', \n", "         fontsize=16, fontweight='bold')\n", "plt.xlabel('Longitude', fontsize=12)\n", "plt.ylabel('Latitude', fontsize=12)\n", "plt.grid(True, linestyle='--', alpha=0.3)\n", "plt.legend(loc='upper right', fontsize=10)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "detailed_metrics", "metadata": {}, "outputs": [], "source": ["# Métricas Detalhadas das Top 5 Cidades\n", "\n", "print(\"📊 ANÁLISE DETALHADA DAS TOP 5 CIDADES\")\n", "print(\"=\" * 70)\n", "\n", "total_population_top5 = top5_cities['population'].sum()\n", "total_regional_pop = top5_cities['regional_population'].sum()\n", "avg_distance = top5_cities['min_dist_existing'].mean()\n", "\n", "print(f\"📈 População Total das Top 5: {total_population_top5:,} habitantes\")\n", "print(f\"🌍 População Regional Total (50km): {total_regional_pop:,} habitantes\")\n", "print(f\"📏 Distância Média de Lojas Existentes: {avg_distance:.1f} km\")\n", "print(f\"💰 Potencial de Mercado Estimado: {total_population_top5 * 0.15:,.0f} clientes potenciais\")\n", "print(f\"   (assumindo 15% de penetração de mercado)\")\n", "\n", "print(\"\\n🎯 RECOMENDAÇÕES ESTRATÉGICAS:\")\n", "print(\"-\" * 40)\n", "\n", "for i, (_, city) in enumerate(top5_cities.iterrows(), 1):\n", "    market_potential = city['population'] * 0.15\n", "    if city['min_dist_existing'] > 50:\n", "        competition = \"Baixa\"\n", "    elif city['min_dist_existing'] > 30:\n", "        competition = \"Média\"\n", "    else:\n", "        competition = \"Alta\"\n", "    \n", "    print(f\"{i}º {city['city']} - {city['state']}:\")\n", "    print(f\"   • Mercado Potencial: {market_potential:,.0f} clientes\")\n", "    print(f\"   • Competição Local: {competition}\")\n", "    print(f\"   • Prioridade: {'🔥 ALTA' if i <= 2 else '⭐ MÉDIA' if i <= 4 else '✅ BAIXA'}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "id": "comparison_table", "metadata": {}, "outputs": [], "source": ["# Tabela Comparativa Completa\n", "\n", "# Criar tabela com todas as métricas importantes\n", "comparison_df = top5_cities[[\n", "    'city', 'state', 'population', 'min_dist_existing', \n", "    'regional_population', 'population_score', 'distance_score', \n", "    'regional_density_score', 'sales_potential_score'\n", "]].copy()\n", "\n", "comparison_df.columns = [\n", "    'Cidade', '<PERSON>stad<PERSON>', 'População', 'Dist_Loja_Próxima_km', \n", "    'Pop_Regional_50km', 'Score_População', 'Score_Distância', \n", "    'Score_Densidade', 'Score_Final'\n", "]\n", "\n", "# Adicionar ranking\n", "comparison_df.insert(0, 'Ranking', range(1, 6))\n", "\n", "# Formatar números\n", "comparison_df['População'] = comparison_df['População'].apply(lambda x: f\"{x:,}\")\n", "comparison_df['Pop_Regional_50km'] = comparison_df['Pop_Regional_50km'].apply(lambda x: f\"{x:,}\")\n", "comparison_df['Dist_Loja_Próxima_km'] = comparison_df['Dist_Loja_Próxima_km'].round(1)\n", "\n", "# Arredondar scores\n", "score_cols = ['Score_População', 'Score_Distância', 'Score_Densidade', 'Score_Final']\n", "for col in score_cols:\n", "    comparison_df[col] = comparison_df[col].round(3)\n", "\n", "print(\"📋 TABELA COMPARATIVA COMPLETA - TOP 5 CIDADES\")\n", "print(\"=\" * 80)\n", "display(comparison_df)\n", "\n", "# Salvar resultado em CSV\n", "comparison_df.to_csv('top5_cidades_potencial_vendas.csv', index=False, encoding='utf-8')\n", "print(\"\\n💾 Resultado salvo em: top5_cidades_potencial_vendas.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "methodology_explanation", "metadata": {}, "outputs": [], "source": ["# Explicação da Metodologia\n", "\n", "print(\"🔬 METODOLOGIA DE ANÁLISE\")\n", "print(\"=\" * 50)\n", "print()\n", "print(\"A análise de potencial de vendas foi baseada em 3 fatores principais:\")\n", "print()\n", "print(\"1️⃣ SCORE DE POPULAÇÃO (40% do peso):\")\n", "print(\"   • <PERSON><PERSON> popula<PERSON> = maior mercado consumidor potencial\")\n", "print(\"   • Normalizado entre 0-1 baseado na população mínima e máxima\")\n", "print()\n", "print(\"2️⃣ SCORE DE DISTÂNCIA (30% do peso):\")\n", "print(\"   • <PERSON><PERSON> distância de lojas existentes = menor canibalização\")\n", "print(\"   • Evita competição direta com lojas próprias\")\n", "print(\"   • Garante cobertura geográfica adequada\")\n", "print()\n", "print(\"3️⃣ SCORE DE DENSIDADE REGIONAL (30% do peso):\")\n", "print(\"   • População total em um raio de 50km\")\n", "print(\"   • Considera o mercado regional ampliado\")\n", "print(\"   • Importante para logística e distribuição\")\n", "print()\n", "print(\"🧮 FÓRMULA FINAL:\")\n", "print(\"Score = 0.40×Score_Pop + 0.30×Score_Dist + 0.30×Score_Densidade\")\n", "print()\n", "print(\"✅ CRITÉRIOS DE EXCLUSÃO:\")\n", "print(\"   • Apenas cidades SEM lojas existentes foram consideradas\")\n", "print(\"   • Evita canibalização do negócio atual\")\n", "print()\n", "print(\"📊 DADOS UTILIZADOS:\")\n", "print(f\"   • Total de cidades analisadas: {len(df)}\")\n", "print(f\"   • Cidades com lojas existentes: {df['existing_store'].sum()}\")\n", "print(f\"   • Cidades candidatas: {len(df_candidates)}\")\n", "print(f\"   • Estado analisado: São Paulo\")"]}, {"cell_type": "code", "execution_count": null, "id": "88de84cd", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Varredura de k para CDs (lojas existentes)\n", "ks = list(range(2, 7))\n", "X_exist = df_existing[['lat','lon']].values\n", "inertias_cd, sil_cd = [], []\n", "for k in ks:\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X_exist)\n", "    inertias_cd.append(km.inertia_)\n", "    sil_cd.append(silhouette_score(X_exist, labels))\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, inertias_cd, marker='o')\n", "ax.set_title(\"CDs · Curva do Cotovelo (Inércia)\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Inércia (WCSS)\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, sil_cd, marker='o')\n", "ax.set_title(\"CDs · Silhouette por k\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Silhouette\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd731610", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Varredura de k para Expansão (ponderado por população)\n", "ks = list(range(2, 7))\n", "X_full = df[['lat','lon']].values\n", "w = df['population'].values.astype(float)\n", "inertias_exp, sil_exp = [], []\n", "for k in ks:\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X_full, sample_weight=w)\n", "    inertias_exp.append(km.inertia_)\n", "    # Silhouette sem peso (aprox.)\n", "    from sklearn.metrics import silhouette_score\n", "    sil_exp.append(silhouette_score(X_full, labels))\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, inertias_exp, marker='o')\n", "ax.set_title(\"Expansão · Curva do Cotovelo (Inércia)\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Inércia (WCSS)\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, sil_exp, marker='o')\n", "ax.set_title(\"<PERSON><PERSON>s<PERSON> · Silhouette por k (aprox.)\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Silhouette\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}