{"cells": [{"cell_type": "code", "execution_count": null, "id": "29c4f2f4", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.cluster import KMeans\n", "from sklearn.metrics import silhouette_score\n", "\n", "# Distância Haversine (km)\n", "def haversine(lat1, lon1, lat2, lon2):\n", "    R = 6371.0\n", "    p1 = np.radians(lat1); p2 = np.radians(lat2)\n", "    dlat = np.radians(lat2 - lat1)\n", "    dlon = np.radians(lon2 - lon1)\n", "    a = np.sin(dlat/2)**2 + np.cos(p1)*np.cos(p2)*np.sin(dlon/2)**2\n", "    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))\n", "    return R * c\n", "\n", "def nearest_city(centroid, df, exclude_existing=False):\n", "    lat, lon = centroid\n", "    dists = haversine(lat, lon, df['lat'].values, df['lon'].values)\n", "    order = np.argsort(dists)\n", "    if not exclude_existing:\n", "        idx = int(order[0])\n", "        row = df.iloc[idx]\n", "        return row['city'], row['state'], float(dists[order[0]])\n", "    # procurar a mais próxima que NÃO seja loja existente\n", "    for idx in order:\n", "        row = df.iloc[int(idx)]\n", "        if not row.get('existing_store', False):\n", "            return row['city'], row['state'], float(dists[int(idx)])\n", "    # fallback: se todas forem existentes, retorna a mais próxima mesmo assim\n", "    idx = int(order[0])\n", "    row = df.iloc[idx]\n", "    return row['city'], row['state'], float(dists[idx])\n"]}, {"cell_type": "code", "execution_count": null, "id": "29f47504", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["sp_data = [\n", "    {'city': 'São Paulo', 'state': 'SP', 'lat': -23.5505, 'lon': -46.6333, 'population': 12000000, 'existing_store': True},\n", "    {'city': 'Guarul<PERSON>', 'state': 'SP', 'lat': -23.4543, 'lon': -46.5337, 'population': 1400000, 'existing_store': False},\n", "    {'city': 'Osasco', 'state': 'SP', 'lat': -23.532, 'lon': -46.7913, 'population': 700000, 'existing_store': False},\n", "    {'city': 'Santo André', 'state': 'SP', 'lat': -23.6637, 'lon': -46.5383, 'population': 720000, 'existing_store': False},\n", "    {'city': 'São Bernardo do Campo', 'state': 'SP', 'lat': -23.689, 'lon': -46.5646, 'population': 840000, 'existing_store': False},\n", "    {'city': '<PERSON>adema', 'state': 'SP', 'lat': -23.6813, 'lon': -46.62, 'population': 420000, 'existing_store': False},\n", "    {'city': 'Barueri', 'state': 'SP', 'lat': -23.5057, 'lon': -46.879, 'population': 280000, 'existing_store': False},\n", "    {'city': 'Jundiaí', 'state': 'SP', 'lat': -23.1857, 'lon': -46.8978, 'population': 440000, 'existing_store': True},\n", "    {'city': 'Campinas', 'state': 'SP', 'lat': -22.9056, 'lon': -47.0608, 'population': 1300000, 'existing_store': True},\n", "    {'city': 'Americana', 'state': 'SP', 'lat': -22.7377, 'lon': -47.3331, 'population': 240000, 'existing_store': False},\n", "    {'city': 'Limeira', 'state': 'SP', 'lat': -22.565, 'lon': -47.4009, 'population': 310000, 'existing_store': False},\n", "    {'city': 'Piracicaba', 'state': 'SP', 'lat': -22.7338, 'lon': -47.6476, 'population': 410000, 'existing_store': False},\n", "    {'city': 'Rio Claro', 'state': 'SP', 'lat': -22.4111, 'lon': -47.561, 'population': 210000, 'existing_store': False},\n", "    {'city': 'Sorocaba', 'state': 'SP', 'lat': -23.5015, 'lon': -47.4526, 'population': 700000, 'existing_store': True},\n", "    {'city': 'Indaiatuba', 'state': 'SP', 'lat': -23.0907, 'lon': -47.2181, 'population': 270000, 'existing_store': False},\n", "    {'city': 'Itu', 'state': 'SP', 'lat': -23.2665, 'lon': -47.2992, 'population': 180000, 'existing_store': False},\n", "    {'city': 'Itapetininga', 'state': 'SP', 'lat': -23.5917, 'lon': -48.053, 'population': 170000, 'existing_store': False},\n", "    {'city': 'Tatuí', 'state': 'SP', 'lat': -23.3556, 'lon': -47.8569, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Botucatu', 'state': 'SP', 'lat': -22.8859, 'lon': -48.4446, 'population': 150000, 'existing_store': False},\n", "    {'city': 'Bauru', 'state': 'SP', 'lat': -22.3246, 'lon': -49.0871, 'population': 380000, 'existing_store': True},\n", "    {'city': 'Ja<PERSON>', 'state': 'SP', 'lat': -22.2966, 'lon': -48.5583, 'population': 150000, 'existing_store': False},\n", "    {'city': 'Marília', 'state': 'SP', 'lat': -22.2171, 'lon': -49.9501, 'population': 230000, 'existing_store': False},\n", "    {'city': 'Ourinhos', 'state': 'SP', 'lat': -22.9797, 'lon': -49.8719, 'population': 115000, 'existing_store': False},\n", "    {'city': 'Assis', 'state': 'SP', 'lat': -22.6619, 'lon': -50.4121, 'population': 105000, 'existing_store': False},\n", "    {'city': 'President<PERSON>', 'state': 'SP', 'lat': -22.1207, 'lon': -51.3925, 'population': 230000, 'existing_store': False},\n", "    {'city': 'Araçatuba', 'state': 'SP', 'lat': -21.206, 'lon': -50.432, 'population': 200000, 'existing_store': False},\n", "    {'city': 'Birigui', 'state': 'SP', 'lat': -21.2887, 'lon': -50.3432, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Penápolis', 'state': 'SP', 'lat': -21.419, 'lon': -50.0773, 'population': 65000, 'existing_store': False},\n", "    {'city': 'Andradina', 'state': 'SP', 'lat': -20.8967, 'lon': -51.3795, 'population': 57000, 'existing_store': False},\n", "    {'city': 'São José do Rio Preto', 'state': 'SP', 'lat': -20.8113, 'lon': -49.3758, 'population': 470000, 'existing_store': True},\n", "    {'city': 'Catanduva', 'state': 'SP', 'lat': -21.1316, 'lon': -48.977, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Barretos', 'state': 'SP', 'lat': -20.5531, 'lon': -48.567, 'population': 120000, 'existing_store': False},\n", "    {'city': 'Franca', 'state': 'SP', 'lat': -20.5386, 'lon': -47.4004, 'population': 370000, 'existing_store': False},\n", "    {'city': 'Ribeirão Preto', 'state': 'SP', 'lat': -21.1775, 'lon': -47.8103, 'population': 720000, 'existing_store': True},\n", "    {'city': 'São Carlos', 'state': 'SP', 'lat': -22.0087, 'lon': -47.8976, 'population': 250000, 'existing_store': False},\n", "    {'city': 'Araraquara', 'state': 'SP', 'lat': -21.7845, 'lon': -48.178, 'population': 240000, 'existing_store': False},\n", "    {'city': '<PERSON>gi das Cruzes', 'state': 'SP', 'lat': -23.5208, 'lon': -46.1855, 'population': 460000, 'existing_store': False},\n", "    {'city': 'Suzano', 'state': 'SP', 'lat': -23.5439, 'lon': -46.311, 'population': 300000, 'existing_store': False},\n", "    {'city': 'Bragança Paulista', 'state': 'SP', 'lat': -22.9527, 'lon': -46.5419, 'population': 180000, 'existing_store': False},\n", "    {'city': 'Atibaia', 'state': 'SP', 'lat': -23.1171, 'lon': -46.5548, 'population': 150000, 'existing_store': False},\n", "    {'city': 'Jacareí', 'state': 'SP', 'lat': -23.2983, 'lon': -45.9658, 'population': 240000, 'existing_store': False},\n", "    {'city': 'São José dos Campos', 'state': 'SP', 'lat': -23.1896, 'lon': -45.8841, 'population': 750000, 'existing_store': True},\n", "    {'city': 'Taubaté', 'state': 'SP', 'lat': -23.0205, 'lon': -45.5569, 'population': 320000, 'existing_store': False},\n", "    {'city': 'Pindamonhangaba', 'state': 'SP', 'lat': -22.9235, 'lon': -45.4614, 'population': 170000, 'existing_store': False},\n", "    {'city': 'Santos', 'state': 'SP', 'lat': -23.959, 'lon': -46.3336, 'population': 430000, 'existing_store': True},\n", "    {'city': 'São Vicente', 'state': 'SP', 'lat': -23.9631, 'lon': -46.3919, 'population': 370000, 'existing_store': False},\n", "    {'city': 'Praia Grande', 'state': 'SP', 'lat': -24.0058, 'lon': -46.402, 'population': 330000, 'existing_store': False}\n", "]\n", "df = pd.DataFrame(sp_data)\n", "print(\"Total de cidades:\", len(df))\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "31fb2f22", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["plt.figure(figsize=(9,6))\n", "mask = df['existing_store']\n", "plt.scatter(df.loc[~mask, 'lon'], df.loc[~mask, 'lat'], s=25, alpha=0.8, label='Cidades sem loja')\n", "plt.scatter(df.loc[mask, 'lon'], df.loc[mask, 'lat'], s=60, alpha=0.9, marker='s', label='Lojas existentes')\n", "for _, row in df.iterrows():\n", "    if row['existing_store']:\n", "        plt.annotate(row['city'], (row['lon'], row['lat']), fontsize=7, alpha=0.8)\n", "plt.title(\"Cidades de SP · Lojas existentes destacadas\")\n", "plt.xlabel(\"Longitude\"); plt.ylabel(\"Latitude\")\n", "plt.grid(True, linestyle='--', alpha=0.3); plt.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "1d22bb4d", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["df_existing = df[df['existing_store']].copy()\n", "print(\"Lojas existentes:\", len(df_existing))\n", "df_existing[['city','state','lat','lon']].reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "31ae4dfe", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def run_cd_scenario(k, df_existing):\n", "    X = df_existing[['lat','lon']].values\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X)\n", "    centroids = km.cluster_centers_\n", "    inertia = km.inertia_\n", "    sil = silhouette_score(X, labels) if k > 1 and k < len(X) else float('nan')\n", "\n", "    # Plot clusters (apenas lojas existentes)\n", "    plt.figure(figsize=(9,6))\n", "    for i in range(k):\n", "        pts = X[labels == i]\n", "        plt.scatter(pts[:,1], pts[:,0], s=45, alpha=0.9, label=f\"Cluster {i+1}\")\n", "    plt.scatter(centroids[:,1], centroids[:,0], s=200, marker='X', edgecolor='black', linewidth=1, label=\"Centróides (CDs)\")\n", "    plt.title(f\"CDs para lojas existentes — k={k}\")\n", "    plt.xlabel(\"Longitude\"); plt.ylabel(\"Latitude\")\n", "    plt.grid(True, linestyle='--', alpha=0.3); plt.legend()\n", "    plt.show()\n", "\n", "    # Cidades candidatas aos CDs (mais próximas dos centróides)\n", "    candidatos = []\n", "    for c in centroids:\n", "        cidade, estado, dist_km = nearest_city(c, df_existing, exclude_existing=False)\n", "        candidatos.append((cidade, estado, dist_km, c[0], c[1]))\n", "    out = pd.DataFrame(candidatos, columns=[\"cidade_candidata\",\"estado\",\"dist_km\",\"centroid_lat\",\"centroid_lon\"])\n", "    display(out.sort_values(\"dist_km\"))\n", "    print(f\"Inércia (WCSS): {inertia:.2f} | Silhouette: {sil:.3f}\")\n", "    return centroids, labels, inertia, sil\n"]}, {"cell_type": "code", "execution_count": null, "id": "45463e46", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cA1 = run_cd_scenario(1, df_existing)"]}, {"cell_type": "code", "execution_count": null, "id": "213370da", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cA2 = run_cd_scenario(2, df_existing)"]}, {"cell_type": "code", "execution_count": null, "id": "aababd70", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cA3 = run_cd_scenario(3, df_existing)"]}, {"cell_type": "code", "execution_count": null, "id": "74477b97", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def run_weighted_expansion(k, df_full):\n", "    X = df_full[['lat','lon']].values\n", "    w = df_full['population'].values.astype(float)\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X, sample_weight=w)\n", "    centroids = km.cluster_centers_\n", "    inertia = km.inertia_\n", "\n", "    # silhouette sem peso (aprox.) — apenas para referência\n", "    sil = silhouette_score(X, labels) if k > 1 and k < len(X) else float('nan')\n", "\n", "    # Plot: cidades sem loja (oportunidade) vs existentes\n", "    plt.figure(figsize=(9,6))\n", "    mask = df_full['existing_store']\n", "    plt.scatter(df_full.loc[~mask,'lon'], df_full.loc[~mask,'lat'], s=20, alpha=0.8, label=\"Cidades sem loja\")\n", "    plt.scatter(df_full.loc[mask,'lon'], df_full.loc[mask,'lat'], s=60, alpha=0.9, marker='s', label=\"Lojas existentes\")\n", "    plt.scatter(centroids[:,1], centroids[:,0], s=220, marker='X', edgecolor='black', linewidth=1, label=\"Centróides (demanda)\")\n", "    plt.title(f\"Clusters de demanda (população) — k={k}\")\n", "    plt.xlabel(\"Longitude\"); plt.ylabel(\"Latitude\")\n", "    plt.grid(True, linestyle='--', alpha=0.3); plt.legend()\n", "    plt.show()\n", "\n", "    # Sugerir cidades candidatas (preferindo não existentes)\n", "    candidatos = []\n", "    for c in centroids:\n", "        cidade, estado, dist_km = nearest_city(c, df_full, exclude_existing=True)\n", "        candidatos.append((cidade, estado, dist_km, c[0], c[1]))\n", "    out = pd.DataFrame(candidatos, columns=[\"cidade_candidata_nova_loja\",\"estado\",\"dist_km\",\"centroid_lat\",\"centroid_lon\"])\n", "    display(out.sort_values(\"dist_km\"))\n", "    print(f\"Inércia (WCSS): {inertia:.2f} | Silhouette (aprox. sem peso): {sil:.3f}\")\n", "    return centroids, labels, inertia, sil"]}, {"cell_type": "code", "execution_count": null, "id": "715d54ca", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cB1 = run_weighted_expansion(1, df)"]}, {"cell_type": "code", "execution_count": null, "id": "692d2004", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cB2 = run_weighted_expansion(2, df)"]}, {"cell_type": "code", "execution_count": null, "id": "d1c9d90a", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["cB3 = run_weighted_expansion(3, df)"]}, {"cell_type": "code", "execution_count": null, "id": "88de84cd", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Varredura de k para CDs (lojas existentes)\n", "ks = list(range(2, 7))\n", "X_exist = df_existing[['lat','lon']].values\n", "inertias_cd, sil_cd = [], []\n", "for k in ks:\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X_exist)\n", "    inertias_cd.append(km.inertia_)\n", "    sil_cd.append(silhouette_score(X_exist, labels))\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, inertias_cd, marker='o')\n", "ax.set_title(\"CDs · Curva do Cotovelo (Inércia)\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Inércia (WCSS)\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, sil_cd, marker='o')\n", "ax.set_title(\"CDs · Silhouette por k\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Silhouette\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd731610", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Varredura de k para Expansão (ponderado por população)\n", "ks = list(range(2, 7))\n", "X_full = df[['lat','lon']].values\n", "w = df['population'].values.astype(float)\n", "inertias_exp, sil_exp = [], []\n", "for k in ks:\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    labels = km.fit_predict(X_full, sample_weight=w)\n", "    inertias_exp.append(km.inertia_)\n", "    # Silhouette sem peso (aprox.)\n", "    from sklearn.metrics import silhouette_score\n", "    sil_exp.append(silhouette_score(X_full, labels))\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, inertias_exp, marker='o')\n", "ax.set_title(\"Expansão · Curva do Cotovelo (Inércia)\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Inércia (WCSS)\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n", "\n", "fig, ax = plt.subplots(figsize=(7,4.5))\n", "ax.plot(ks, sil_exp, marker='o')\n", "ax.set_title(\"<PERSON><PERSON>s<PERSON> · Silhouette por k (aprox.)\")\n", "ax.set_xlabel(\"k\"); ax.set_ylabel(\"Silhouette\")\n", "ax.grid(True, linestyle='--', alpha=0.3)\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}